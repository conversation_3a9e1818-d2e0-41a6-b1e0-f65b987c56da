name: Build and Test

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  build:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Setup MSBuild
        uses: microsoft/setup-msbuild@v1.3

      - name: Install dependencies
        run: npm ci

      - name: Rebuild native modules
        run: npm run rebuild

      - name: Build application (test)
        run: npm run build
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: List build artifacts
        run: |
          echo "Build completed successfully!"
          if (Test-Path "dist") {
            echo "Contents of dist directory:"
            Get-ChildItem -Path "dist" -Recurse | Format-Table Name, Length, LastWriteTime
          } else {
            echo "No dist directory found"
          }
        shell: powershell
        continue-on-error: true
