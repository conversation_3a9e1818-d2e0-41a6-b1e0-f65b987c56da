name: Build and Test

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

permissions:
  contents: read
  actions: read

jobs:
  build:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        run: |
          git clone https://github.com/${{ github.repository }}.git .
          git checkout ${{ github.sha }}

      - name: Setup Node.js
        run: |
          choco install nodejs --version=18.19.0 -y
          refreshenv
          node --version
          npm --version

      - name: Setup Python
        run: |
          choco install python --version=3.11.7 -y
          refreshenv
          python --version

      - name: Setup MSBuild
        run: |
          echo "MSBuild should be available on windows-latest runner"
          where msbuild

      - name: Install dependencies
        run: npm ci

      - name: Rebuild native modules
        run: npm run rebuild

      - name: Build application (test)
        run: npm run build
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: List build artifacts
        run: |
          echo "Build completed successfully!"
          if (Test-Path "dist") {
            echo "Contents of dist directory:"
            Get-ChildItem -Path "dist" -Recurse | Format-Table Name, Length, LastWriteTime
          } else {
            echo "No dist directory found"
          }
        shell: powershell
        continue-on-error: true
