name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

jobs:
  release:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Setup MSBuild
        uses: microsoft/setup-msbuild@v1.3

      - name: Install dependencies
        run: npm ci

      - name: Rebuild native modules
        run: npm run rebuild

      - name: Build application
        run: npm run build
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version from tag
        id: get_version
        shell: bash
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
          else
            echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
          fi

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.get_version.outputs.version }}
          release_name: Blade Ball Master ${{ steps.get_version.outputs.version }}
          body: |
            ## Changes in ${{ steps.get_version.outputs.version }}
            
            ### Features
            - Auto-generated release for Blade Ball Master
            
            ### Downloads
            - **Portable**: Download the portable executable
            - **Installer**: Download the NSIS installer
            
            ### Installation
            1. Download the appropriate file for your system
            2. Run the installer or extract the portable version
            3. Enjoy the application!
          draft: false
          prerelease: false

      - name: Upload Portable Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./dist/Blade Ball Master ${{ steps.get_version.outputs.version }}.exe
          asset_name: BladeBallMaster-${{ steps.get_version.outputs.version }}-portable.exe
          asset_content_type: application/octet-stream
        continue-on-error: true

      - name: Upload NSIS Installer
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./dist/Blade Ball Master Setup ${{ steps.get_version.outputs.version }}.exe
          asset_name: BladeBallMaster-${{ steps.get_version.outputs.version }}-setup.exe
          asset_content_type: application/octet-stream
        continue-on-error: true

      - name: List dist directory (for debugging)
        run: |
          echo "Contents of dist directory:"
          dir dist
        continue-on-error: true
