name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

permissions:
  contents: write
  actions: read
  packages: write

jobs:
  release:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        run: |
          git clone https://github.com/${{ github.repository }}.git .
          git checkout ${{ github.sha }}

      - name: Setup Node.js
        run: |
          choco install nodejs --version=18.19.0 -y
          refreshenv
          node --version
          npm --version

      - name: Setup Python
        run: |
          choco install python --version=3.11.7 -y
          refreshenv
          python --version

      - name: Setup MSBuild
        run: |
          echo "MSBuild should be available on windows-latest runner"
          where msbuild

      - name: Install dependencies
        run: npm ci

      - name: Rebuild native modules
        run: npm run rebuild

      - name: Build application
        run: npm run build
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version from tag
        id: get_version
        shell: bash
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            TAG_VERSION="${{ github.event.inputs.version }}"
          else
            TAG_VERSION="${GITHUB_REF#refs/tags/}"
          fi

          # Remove 'v' prefix if present for file naming
          VERSION_NUMBER="${TAG_VERSION#v}"

          echo "tag_version=$TAG_VERSION" >> $GITHUB_OUTPUT
          echo "version_number=$VERSION_NUMBER" >> $GITHUB_OUTPUT
          echo "Using tag version: $TAG_VERSION"
          echo "Using version number for files: $VERSION_NUMBER"

      - name: Create Release
        id: create_release
        shell: bash
        run: |
          TAG_VERSION="${{ steps.get_version.outputs.tag_version }}"
          VERSION_NUMBER="${{ steps.get_version.outputs.version_number }}"
          RELEASE_BODY="## Changes in $TAG_VERSION

          ### Features
          - Auto-generated release for Blade Ball Master

          ### Downloads
          - **Portable**: Download the portable executable
          - **Installer**: Download the NSIS installer

          ### Installation
          1. Download the appropriate file for your system
          2. Run the installer or extract the portable version
          3. Enjoy the application!"

          # Create release using GitHub API
          RESPONSE=$(curl -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/releases \
            -d "{
              \"tag_name\": \"$TAG_VERSION\",
              \"name\": \"Blade Ball Master $TAG_VERSION\",
              \"body\": $(echo "$RELEASE_BODY" | jq -R -s .),
              \"draft\": false,
              \"prerelease\": false
            }")

          echo "Release response: $RESPONSE"
          UPLOAD_URL=$(echo "$RESPONSE" | jq -r '.upload_url' | sed 's/{?name,label}//')
          echo "upload_url=$UPLOAD_URL" >> $GITHUB_OUTPUT

      - name: Upload Portable Release Asset
        shell: bash
        run: |
          VERSION="${{ steps.get_version.outputs.version_number }}"
          UPLOAD_URL="${{ steps.create_release.outputs.upload_url }}"
          ASSET_PATH="./dist/Blade Ball Master $VERSION.exe"
          ASSET_NAME="BladeBallMaster-$VERSION-portable.exe"

          if [ -f "$ASSET_PATH" ]; then
            curl -X POST \
              -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              -H "Content-Type: application/octet-stream" \
              --data-binary @"$ASSET_PATH" \
              "$UPLOAD_URL?name=$ASSET_NAME"
            echo "Uploaded portable executable"
          else
            echo "Portable executable not found at: $ASSET_PATH"
          fi
        continue-on-error: true

      - name: Upload NSIS Installer
        shell: bash
        run: |
          VERSION="${{ steps.get_version.outputs.version_number }}"
          UPLOAD_URL="${{ steps.create_release.outputs.upload_url }}"
          ASSET_PATH="./dist/Blade Ball Master Setup $VERSION.exe"
          ASSET_NAME="BladeBallMaster-$VERSION-setup.exe"

          if [ -f "$ASSET_PATH" ]; then
            curl -X POST \
              -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              -H "Content-Type: application/octet-stream" \
              --data-binary @"$ASSET_PATH" \
              "$UPLOAD_URL?name=$ASSET_NAME"
            echo "Uploaded NSIS installer"
          else
            echo "NSIS installer not found at: $ASSET_PATH"
          fi
        continue-on-error: true

      - name: List dist directory (for debugging)
        run: |
          echo "Contents of dist directory:"
          dir dist
        continue-on-error: true
