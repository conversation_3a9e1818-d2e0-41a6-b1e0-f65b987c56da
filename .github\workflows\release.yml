name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

permissions:
  contents: write
  actions: read
  packages: write

jobs:
  release:
    runs-on: windows-latest

    steps:
      - name: Checkout code
        run: |
          git clone https://github.com/${{ github.repository }}.git .
          git checkout ${{ github.sha }}

      - name: Setup Node.js
        run: |
          choco install nodejs --version=18.19.0 -y
          refreshenv
          node --version
          npm --version

      - name: Setup Python
        run: |
          choco install python --version=3.11.7 -y
          refreshenv
          python --version

      - name: Setup MSBuild
        run: |
          echo "MSBuild should be available on windows-latest runner"
          where msbuild

      - name: Install dependencies
        run: npm ci

      - name: Rebuild native modules
        run: npm run rebuild

      - name: Build application
        run: npm run build
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get version from tag
        id: get_version
        shell: bash
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            TAG_VERSION="${{ github.event.inputs.version }}"
          else
            TAG_VERSION="${GITHUB_REF#refs/tags/}"
          fi

          # Remove 'v' prefix if present for file naming
          VERSION_NUMBER="${TAG_VERSION#v}"

          echo "tag_version=$TAG_VERSION" >> $GITHUB_OUTPUT
          echo "version_number=$VERSION_NUMBER" >> $GITHUB_OUTPUT
          echo "Using tag version: $TAG_VERSION"
          echo "Using version number for files: $VERSION_NUMBER"

      - name: Create Release
        id: create_release
        shell: bash
        run: |
          TAG_VERSION="${{ steps.get_version.outputs.tag_version }}"
          VERSION_NUMBER="${{ steps.get_version.outputs.version_number }}"

          # Create release using GitHub API with simpler body
          RESPONSE=$(curl -s -X POST \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            https://api.github.com/repos/${{ github.repository }}/releases \
            -d "{
              \"tag_name\": \"$TAG_VERSION\",
              \"name\": \"Blade Ball Master $TAG_VERSION\",
              \"body\": \"## Changes in $TAG_VERSION\\n\\n### Features\\n- Auto-generated release for Blade Ball Master\\n\\n### Downloads\\n- **Portable**: Download the portable executable\\n- **Installer**: Download the NSIS installer\",
              \"draft\": false,
              \"prerelease\": false
            }")

          echo "Release response: $RESPONSE"

          # Check if response contains an error
          if echo "$RESPONSE" | grep -q '"message"'; then
            echo "Error creating release: $RESPONSE"
            exit 1
          fi

          UPLOAD_URL=$(echo "$RESPONSE" | jq -r '.upload_url' | sed 's/{?name,label}//')
          echo "Upload URL: $UPLOAD_URL"
          echo "upload_url=$UPLOAD_URL" >> $GITHUB_OUTPUT

      - name: Upload Portable Release Asset
        shell: bash
        run: |
          VERSION="${{ steps.get_version.outputs.version_number }}"
          UPLOAD_URL="${{ steps.create_release.outputs.upload_url }}"
          ASSET_PATH="./dist/Blade Ball Master $VERSION.exe"
          ASSET_NAME="BladeBallMaster-$VERSION-portable.exe"

          echo "Upload URL: $UPLOAD_URL"
          echo "Asset path: $ASSET_PATH"
          echo "Asset name: $ASSET_NAME"

          if [ "$UPLOAD_URL" = "null" ] || [ -z "$UPLOAD_URL" ]; then
            echo "Error: Upload URL is null or empty. Release creation may have failed."
            exit 1
          fi

          if [ -f "$ASSET_PATH" ]; then
            echo "File found, uploading..."
            curl -X POST \
              -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
              -H "Content-Type: application/octet-stream" \
              --data-binary @"$ASSET_PATH" \
              "$UPLOAD_URL?name=$ASSET_NAME"
            echo "Uploaded portable executable"
          else
            echo "Portable executable not found at: $ASSET_PATH"
            ls -la "./dist/"
          fi
        continue-on-error: true

      - name: Upload NSIS Installer
        shell: bash
        run: |
          VERSION="${{ steps.get_version.outputs.version_number }}"
          UPLOAD_URL="${{ steps.create_release.outputs.upload_url }}"
          ASSET_PATH="./dist/Blade Ball Master Setup $VERSION.exe"
          ASSET_NAME="BladeBallMaster-$VERSION-setup.exe"

          echo "Upload URL: $UPLOAD_URL"
          echo "Asset path: $ASSET_PATH"
          echo "Asset name: $ASSET_NAME"

          if [ "$UPLOAD_URL" = "null" ] || [ -z "$UPLOAD_URL" ]; then
            echo "Error: Upload URL is null or empty. Release creation may have failed."
            exit 1
          fi

          if [ -f "$ASSET_PATH" ]; then
            echo "File found, uploading..."
            curl -X POST \
              -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
              -H "Content-Type: application/octet-stream" \
              --data-binary @"$ASSET_PATH" \
              "$UPLOAD_URL?name=$ASSET_NAME"
            echo "Uploaded NSIS installer"
          else
            echo "NSIS installer not found at: $ASSET_PATH"
            ls -la "./dist/"
          fi
        continue-on-error: true

      - name: List dist directory (for debugging)
        run: |
          echo "Contents of dist directory:"
          dir dist
        continue-on-error: true
