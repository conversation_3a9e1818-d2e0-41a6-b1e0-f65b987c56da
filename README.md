# Blade Ball Master - Advanced Auto-Clicker with Roblox Integration

Blade Ball Master is a powerful auto-clicker application specifically designed for Roblox games, with special optimizations for Blade Ball. It features a sleek, modern UI with advanced functionality including butterfly clicking, custom key bindings, and direct Roblox game launching capabilities.

![Blade Ball Master Screenshot](https://i.imgur.com/placeholder.png)

## Features

- **Advanced Auto-Clicking**:
  - Standard auto-clicking with adjustable click delay
  - Butterfly mode for alternating key presses
  - Custom key bindings for all click functions
  - Real-time click counter and statistics

- **Direct Roblox Game Launching**:
  - Launch Blade Ball with a single click
  - Launch any Roblox game using Place ID
  - Recent games history for quick access

- **Bloxstrap Integration**:
  - Automatic detection of Bloxstrap installation
  - Apply optimized FFlags for Blade Ball performance
  - Manage Roblox client settings directly

- **Modern UI**:
  - Sleek, customizable dark-themed interface
  - Multiple theme options
  - Fullscreen support
  - Real-time status indicators

- **Performance Optimizations**:
  - C++ addon for high-performance clicking
  - Low CPU usage even during intensive clicking
  - Optimized for Blade Ball gameplay

## Prerequisites

- **Windows 10/11** (64-bit)
- **Node.js** (v16+) - Required only for development
- **Roblox** or **Bloxstrap** installed

## Installation

### Method 1: Install from Release (Recommended)

1. Download the latest release from the [Releases page](https://github.com/yourusername/blade-ball-master/releases)
2. Run the installer (`BladeBallMaster-Setup-x.x.x.exe`)
3. Follow the installation wizard
4. Launch the application from your desktop or start menu

### Method 2: Build from Source

#### Step 1: Install Prerequisites

1. Install [Node.js](https://nodejs.org/) (v16 or higher)
2. Install [Visual Studio Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/) with C++ workload
3. Install [Python](https://www.python.org/downloads/) (for node-gyp)

#### Step 2: Clone and Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/blade-ball-master.git
   cd blade-ball-master
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Rebuild the C++ addon for Electron:
   ```bash
   npm run rebuild
   ```

#### Step 3: Run or Build

- **To run in development mode**:
  ```bash
  npm start
  ```

- **To build the installer**:
  ```bash
  npm run build
  ```
  The installer will be created in the `dist` folder.

## Using the Application

### Auto-Clicker

1. **Basic Auto-Clicking**:
   - Toggle the "Auto Click" switch to enable/disable
   - Adjust click delay using the slider
   - Press the assigned key (default: Right Mouse Button) to activate clicking

2. **Butterfly Mode**:
   - Toggle "Butterfly Mode" to enable alternating key presses
   - Use the assigned keys (default: F and G) to activate butterfly clicking

3. **Key Bindings**:
   - Click on any key binding to change it
   - Press the desired key when prompted

### Roblox Game Launcher

1. **Launch Blade Ball**:
   - Click the "Launch Game" button in the Game Launcher section

2. **Launch Custom Game**:
   - Enter a Roblox Place ID in the input field
   - Click "Launch" to start the game

3. **Recent Games**:
   - Click on any game in the Recent Games list to launch it again

### Bloxstrap Integration

1. **Apply Optimized FFlags**:
   - Click "Add FFlags" to apply performance optimizations for Blade Ball
   - Click "Remove FFlags" to restore default settings

## Troubleshooting

### Common Issues

1. **Application won't start**:
   - Make sure you have administrative privileges
   - Check if your antivirus is blocking the application
   - Reinstall the application

2. **Auto-clicker not working**:
   - Ensure Roblox is running and in focus
   - Check if the correct key bindings are set
   - Restart the application

3. **Game launcher not working**:
   - Verify that Roblox or Bloxstrap is properly installed
   - Check your internet connection
   - Make sure the Place ID is correct

### Getting Help

If you encounter any issues not covered here, please:
- Check the [Issues](https://github.com/yourusername/blade-ball-master/issues) page
- Create a new issue with detailed information about your problem

## Development

### Project Structure

- `main.js` - Main Electron process
- `preload.js` - Preload script for Node.js integration
- `renderer.js` - UI interaction logic
- `index.html` - Main application UI
- `styles.css` - UI styling
- `cpp/` - C++ addon source code
- `binding.gyp` - Build configuration for the C++ addon
- `package.json` - Project configuration and dependencies

### Complete Integration Process

This section explains how all components of the application work together:

#### 1. Application Startup Flow

1. **Electron Initialization**:
   - `main.js` creates the application window
   - Sets up IPC handlers for game launching
   - Configures window properties and behavior

2. **Preload Script Execution**:
   - `preload.js` runs in a privileged context
   - Loads the C++ addon and exposes it to the renderer
   - Bridges Node.js APIs to the renderer process

3. **Renderer Process**:
   - `index.html` loads the UI structure
   - `styles.css` applies styling and animations
   - `renderer.js` initializes UI interactions and event handlers

#### 2. Component Communication

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│   Main Process  │◄────►│  Preload Script │◄────►│ Renderer Process│
│    (main.js)    │ IPC  │  (preload.js)   │ APIs │ (renderer.js)   │
│                 │      │                 │      │                 │
└────────┬────────┘      └────────┬────────┘      └────────┬────────┘
         │                        │                        │
         │                        ▼                        │
         │               ┌─────────────────┐               │
         │               │                 │               │
         └──────────────►│    C++ Addon    │◄──────────────┘
                         │ (cpp/addon.cpp) │
                         │                 │
                         └─────────────────┘
```

- **IPC Communication**: Main and renderer processes communicate via Electron's IPC
- **C++ Addon Access**: Both main and renderer can access C++ functionality
- **UI Updates**: Renderer process updates UI based on C++ addon state

#### 3. Data Flow for Key Features

**Auto-Clicking Feature**:
1. User toggles auto-click in UI
2. Renderer calls C++ addon's `toggleAutoClick()`
3. C++ addon updates internal state
4. Status check thread monitors key presses
5. When conditions are met, clicking thread simulates clicks
6. UI is updated with click counter and status

**Game Launching Feature**:
1. User enters Place ID or selects a game
2. Renderer sends IPC message to main process
3. Main process uses shell integration to launch Roblox
4. Main process updates recent games list
5. Renderer displays success/error notification

**Bloxstrap Integration**:
1. Application checks for Bloxstrap installation
2. If found, reads current FFlag configuration
3. User can apply or remove optimization FFlags
4. Application writes changes to Bloxstrap's config file

#### 4. Building and Packaging

1. **Development Build**:
   - Run `npm start` to launch in development mode
   - Changes to JavaScript/HTML/CSS apply on reload
   - C++ changes require rebuild with `npm run rebuild`

2. **Production Build**:
   - Run `npm run build` to create installer
   - Electron-builder packages all components
   - C++ addon is included in the `app.asar.unpacked` directory
   - Native dependencies are properly bundled

3. **Distribution**:
   - Windows installer (.exe) is created in `dist/` folder
   - Application can be installed by end users
   - Auto-updates can be configured (optional)

## C++ Addon Integration

The heart of Blade Ball Master's performance is its C++ addon, which provides high-speed, low-latency clicking functionality. This section explains how the C++ addon works and how to modify or extend it.

### C++ Addon Architecture

The C++ addon (`cpp/addon.cpp`) is built using Node-API (N-API) and provides the following core functionality:

1. **High-Performance Clicking**: Uses Windows API (`SendInput`) for precise mouse input simulation
2. **Butterfly Clicking**: Implements alternating key press detection for advanced clicking techniques
3. **Roblox Window Detection**: Automatically detects when Roblox is the active window
4. **Multi-threaded Operation**: Uses separate threads for UI updates and clicking operations
5. **Key Binding System**: Allows custom key bindings for all clicking functions

### Key Components

- **Thread Management**: Two main threads handle clicking and status monitoring
- **Atomic Variables**: Thread-safe variables for state management across threads
- **Windows API Integration**: Uses Windows-specific APIs for input simulation and window detection
- **N-API Exports**: Exposes C++ functions to JavaScript through Node-API

### Building the C++ Addon

#### Prerequisites

To build the C++ addon, you need:

1. **Visual Studio Build Tools** with C++ workload
2. **Python** (for node-gyp)
3. **node-addon-api** npm package

#### Build Process

1. The build configuration is defined in `binding.gyp`:
   ```json
   {
     "targets": [{
       "target_name": "cpp_addon",
       "sources": ["cpp/addon.cpp"],
       "include_dirs": ["<!@(node -p \"require('node-addon-api').include\")"],
       "libraries": ["user32.lib"]
     }]
   }
   ```

2. To rebuild the addon after making changes:
   ```bash
   npm run rebuild
   ```

3. The build process:
   - Compiles the C++ code with the appropriate flags
   - Links against required libraries (user32.lib for Windows API)
   - Generates a `.node` file that can be loaded by Node.js

### Modifying the C++ Addon

#### Adding New Functions

To add a new function to the C++ addon:

1. Define your C++ function with the N-API signature:
   ```cpp
   Napi::Value YourFunction(const Napi::CallbackInfo& info) {
     Napi::Env env = info.Env();
     // Your implementation here
     return Napi::Boolean::New(env, true);
   }
   ```

2. Export the function in the `Init` function:
   ```cpp
   exports.Set(Napi::String::New(env, "yourFunction"),
               Napi::Function::New(env, YourFunction));
   ```

3. Access it from JavaScript:
   ```javascript
   window.cppAddon.yourFunction();
   ```

#### Modifying Clicking Behavior

The clicking behavior is controlled by these key functions:

- `clickingThread()`: Main clicking loop for standard clicking
- `performDoubleClick()`: Executes butterfly clicking
- `statusCheckThread()`: Monitors key states and window focus

#### Example: Changing Click Pattern

To modify the click pattern (e.g., adding triple-clicking):

```cpp
void performTripleClick() {
  INPUT input = { 0 };
  input.type = INPUT_MOUSE;

  // Execute three clicks in sequence
  for (int i = 0; i < 3; i++) {
    input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    SendInput(1, &input, sizeof(INPUT));
    input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    SendInput(1, &input, sizeof(INPUT));

    if (i < 2) Sleep(1); // Small delay between clicks
  }

  clickCounter += 3; // Increment counter
}
```

### JavaScript Integration

The C++ addon is loaded in `preload.js` and made available to the renderer process:

```javascript
// In preload.js
const cppAddon = findAddon();
window.cppAddon = cppAddon;

// In renderer.js
const startClicker = () => {
  if (window.cppAddon) {
    window.cppAddon.startClicker();
  }
};
```

### Troubleshooting C++ Addon Issues

Common issues and solutions:

1. **Build Errors**:
   - Ensure Visual Studio Build Tools are correctly installed
   - Check that Python is in your PATH
   - Run `npm install --global windows-build-tools` if needed

2. **Runtime Errors**:
   - Check for missing Windows libraries
   - Ensure proper error handling in C++ code
   - Use try/catch blocks when calling C++ functions from JavaScript

3. **Performance Issues**:
   - Use atomic variables for thread-safe operations
   - Minimize Sleep durations for better responsiveness
   - Use busy-wait loops for very short delays

### Advanced: Adding New Features

To add a new feature (e.g., custom click patterns):

1. Define the feature in C++:
   ```cpp
   std::atomic<int> clickPattern(0); // 0=single, 1=double, 2=triple

   Napi::Value SetClickPattern(const Napi::CallbackInfo& info) {
     Napi::Env env = info.Env();
     int pattern = info[0].As<Napi::Number>().Int32Value();
     clickPattern.store(pattern);
     return Napi::Number::New(env, pattern);
   }
   ```

2. Export it in the `Init` function
3. Create UI controls in HTML/CSS
4. Connect the UI to the C++ function in renderer.js

### Creating a New C++ Addon from Scratch

If you want to create a similar C++ addon for your own Electron project, follow these steps:

#### Step 1: Set Up Project Structure

1. Create the necessary directories:
   ```bash
   mkdir -p cpp
   ```

2. Install required dependencies:
   ```bash
   npm install --save node-addon-api
   npm install --save-dev node-gyp electron-rebuild
   ```

3. Add scripts to package.json:
   ```json
   "scripts": {
     "rebuild": "electron-rebuild -f -w cpp_addon",
     "build": "node-gyp rebuild"
   }
   ```

#### Step 2: Create the binding.gyp File

Create a `binding.gyp` file in the root directory:

```json
{
  "targets": [{
    "target_name": "cpp_addon",
    "cflags!": [ "-fno-exceptions" ],
    "cflags_cc!": [ "-fno-exceptions" ],
    "sources": [ "cpp/addon.cpp" ],
    "include_dirs": [
      "<!@(node -p \"require('node-addon-api').include\")"
    ],
    "defines": [ "NAPI_DISABLE_CPP_EXCEPTIONS" ],
    "conditions": [
      ["OS=='win'", {
        "msvs_settings": {
          "VCCLCompilerTool": {
            "ExceptionHandling": 1
          }
        }
      }]
    ],
    "libraries": [
      "user32.lib"  // For Windows API functions
    ]
  }]
}
```

#### Step 3: Create a Basic C++ Addon

Create `cpp/addon.cpp` with a minimal structure:

```cpp
#include <napi.h>

// Example function
Napi::Value ExampleFunction(const Napi::CallbackInfo& info) {
  Napi::Env env = info.Env();
  return Napi::String::New(env, "Hello from C++!");
}

// Initialize the addon
Napi::Object Init(Napi::Env env, Napi::Object exports) {
  exports.Set(Napi::String::New(env, "exampleFunction"),
              Napi::Function::New(env, ExampleFunction));
  return exports;
}

NODE_API_MODULE(cpp_addon, Init)
```

#### Step 4: Load the Addon in Preload Script

In your preload.js file:

```javascript
const path = require('path');

function findAddon() {
  try {
    // For development
    return require('./build/Release/cpp_addon.node');
  } catch (err) {
    try {
      // For production (packaged app)
      return require(path.join(process.resourcesPath, 'app.asar.unpacked/build/Release/cpp_addon.node'));
    } catch (err2) {
      console.error('Failed to load C++ addon:', err2);
      return null;
    }
  }
}

window.addEventListener('DOMContentLoaded', () => {
  const addon = findAddon();
  if (addon) {
    window.cppAddon = addon;
    console.log('C++ addon loaded successfully');
  }
});
```

#### Step 5: Build the Addon

```bash
npm run rebuild
```

#### Step 6: Use the Addon in Renderer

```javascript
document.getElementById('test-button').addEventListener('click', () => {
  if (window.cppAddon) {
    const result = window.cppAddon.exampleFunction();
    console.log(result); // "Hello from C++"
  }
});
```

This provides a foundation that you can expand with more complex functionality like the auto-clicker features in Blade Ball Master.

### Creating a Production Build

To create an installer:

```bash
npm run build
```

## License

MIT

## Acknowledgements

- [Electron](https://www.electronjs.org/)
- [Node.js](https://nodejs.org/)
- [Roblox](https://www.roblox.com/)
- [Bloxstrap](https://github.com/pizzaboxer/bloxstrap)
