#include <napi.h>
#include <string>
#include <iostream>
#include <map>
#include <windows.h>
#include <thread>
#include <vector>
#include <atomic>
#include <algorithm>
#include <cmath>

// Global flags for thread control
std::atomic<bool> isRunning(true);
std::atomic<bool> shouldClick(false);
std::atomic<int> clickDelay(5); // Default 5ms delay between clicks
std::atomic<int> clickCounter(0);
std::atomic<bool> isRobloxActive(false);
std::atomic<bool> showUI(true); // Toggle UI visibility
std::atomic<int> clickKey(VK_RBUTTON); // Default to right mouse button
std::atomic<bool> autoClickEnabled(false); // Toggle for auto-clicking

// Butterfly clicking variables
std::atomic<bool> butterflyMode(true); // Enable butterfly clicking by default
std::atomic<int> butterflyKey1('F'); // First butterfly key (F)
std::atomic<int> butterflyKey2('G'); // Second butterfly key (G)
std::atomic<bool> key1Pressed(false); // Track key1 state
std::atomic<bool> key2Pressed(false); // Track key2 state

// Thread handles
std::thread uiThread;
std::thread clickThread;

// Function declarations
bool isRobloxWindowActive();
void clickingThread();
void statusCheckThread();
std::string getKeyName(int keyCode);
void startClickerThreads();
void stopClickerThreads();
void performDoubleClick();

// Function to perform double click
void performDoubleClick() {
    INPUT input = { 0 };
    input.type = INPUT_MOUSE;

    // First click
    input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    SendInput(1, &input, sizeof(INPUT));
    input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    SendInput(1, &input, sizeof(INPUT));

    // Small delay between clicks (1ms)
    Sleep(1);

    // Second click
    input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
    SendInput(1, &input, sizeof(INPUT));
    input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
    SendInput(1, &input, sizeof(INPUT));

    clickCounter += 2; // Increment by 2 for butterfly clicking
}

// Clicking thread
void clickingThread() {
    INPUT input = { 0 };
    input.type = INPUT_MOUSE;
    bool wasClicking = false;

    while (isRunning) {
        if (butterflyMode.load()) {
            // Butterfly clicking mode - handle individual keypresses
            // This logic is handled in statusCheckThread for butterfly mode
            Sleep(1);
        } else {
            // Legacy hold-to-click mode
            bool currentlyClicking = shouldClick;

            // Only process clicking when state is true
            if (currentlyClicking) {
                // Combined down and up click
                input.mi.dwFlags = MOUSEEVENTF_LEFTDOWN;
                SendInput(1, &input, sizeof(INPUT));
                input.mi.dwFlags = MOUSEEVENTF_LEFTUP;
                SendInput(1, &input, sizeof(INPUT));

                clickCounter++;

                // Only sleep if needed, and use minimal delay
                if (clickDelay > 0) {
                    // Use a more precise sleep method for short delays
                    if (clickDelay < 10) {
                        // For very short delays, use a busy-wait approach
                        DWORD startTime = GetTickCount();
                        while ((GetTickCount() - startTime) < static_cast<DWORD>(clickDelay) && shouldClick) {
                            // Check if we should still be clicking during the wait
                            if (!shouldClick) break;
                            // Yield to other threads but don't sleep
                            SwitchToThread();
                        }
                    }
                    else {
                        // For longer delays, use shorter sleep intervals with checks
                        int remainingDelay = clickDelay;
                        while (remainingDelay > 0 && shouldClick) {
                            int sleepTime = (remainingDelay < 10) ? remainingDelay : 10;
                            Sleep(sleepTime);
                            remainingDelay -= sleepTime;
                            // Check if we should still be clicking
                            if (!shouldClick) break;
                        }
                    }
                }
            }
            else {
                // Very short sleep when not clicking for better responsiveness
                Sleep(1);
            }

            wasClicking = currentlyClicking;
        }
    }
}

// Thread for checking Roblox window status
void statusCheckThread() {
    bool insertKeyPressed = false;
    bool prevKey1State = false;
    bool prevKey2State = false;
    int windowCheckCounter = 0;
    const int WINDOW_CHECK_INTERVAL = 20; // Only check window title every 20 iterations

    while (isRunning) {
        // Update Roblox active status (but not on every iteration)
        windowCheckCounter++;
        if (windowCheckCounter >= WINDOW_CHECK_INTERVAL) {
            isRobloxActive.store(isRobloxWindowActive());
            windowCheckCounter = 0;
        }

        // Check for Insert key to toggle auto-clicker instead of UI visibility
        bool insertKeyDown = (GetAsyncKeyState(VK_INSERT) & 0x8000) != 0;
        if (insertKeyDown && !insertKeyPressed) {
            autoClickEnabled.store(!autoClickEnabled.load());
        }
        insertKeyPressed = insertKeyDown;

        // Only process clicks if auto-click is enabled and Roblox is active
        if (autoClickEnabled.load() && isRobloxActive.load()) {
            if (butterflyMode.load()) {
                // Butterfly clicking mode - detect individual keypresses
                bool currentKey1State = (GetAsyncKeyState(butterflyKey1.load()) & 0x8000) != 0;
                bool currentKey2State = (GetAsyncKeyState(butterflyKey2.load()) & 0x8000) != 0;

                // Check for key1 press (transition from not pressed to pressed)
                if (currentKey1State && !prevKey1State) {
                    performDoubleClick();
                }

                // Check for key2 press (transition from not pressed to pressed)
                if (currentKey2State && !prevKey2State) {
                    performDoubleClick();
                }

                // Update previous states
                prevKey1State = currentKey1State;
                prevKey2State = currentKey2State;

                // Set shouldClick for UI feedback
                shouldClick.store(currentKey1State || currentKey2State);
            } else {
                // Legacy hold-to-click mode
                shouldClick.store((GetAsyncKeyState(clickKey.load()) & 0x8000) != 0);
            }
        } else {
            shouldClick.store(false);
        }

        // Short sleep for responsiveness
        Sleep(5); // Reduced sleep for better butterfly click detection
    }
}

bool isRobloxWindowActive() {
    // Look for various Roblox window titles
    const char* robloxTitles[] = {
        "Roblox",
        "Roblox Player",
        "Roblox Studio"
    };

    HWND foregroundWindow = GetForegroundWindow();
    if (foregroundWindow == NULL) return false;

    char windowTitle[256];
    GetWindowTextA(foregroundWindow, windowTitle, sizeof(windowTitle));

    // Check if the window title contains any of the Roblox titles
    for (const char* title : robloxTitles) {
        if (strstr(windowTitle, title) != NULL) {
            return true;
        }
    }

    return false;
}

// Get key name from virtual key code
std::string getKeyName(int keyCode) {
    static const std::vector<std::pair<int, std::string>> keyNames = {
        {VK_LBUTTON, "Left Mouse"},
        {VK_RBUTTON, "Right Mouse"},
        {VK_MBUTTON, "Middle Mouse"},
        {VK_SPACE, "Space"},
        {VK_SHIFT, "Shift"},
        {VK_CONTROL, "Ctrl"},
        {VK_MENU, "Alt"},
        {VK_TAB, "Tab"},
        {VK_RETURN, "Enter"},
        {VK_F1, "F1"}, {VK_F2, "F2"}, {VK_F3, "F3"}, {VK_F4, "F4"},
        {VK_F5, "F5"}, {VK_F6, "F6"}, {VK_F7, "F7"}, {VK_F8, "F8"},
        {VK_F9, "F9"}, {VK_F10, "F10"}, {VK_F11, "F11"}, {VK_F12, "F12"}
    };

    // Check for letter keys (A-Z)
    if (keyCode >= 'A' && keyCode <= 'Z') {
        return std::string(1, static_cast<char>(keyCode));
    }

    // Check for number keys (0-9)
    if (keyCode >= '0' && keyCode <= '9') {
        return std::string(1, static_cast<char>(keyCode));
    }

    // Check for special keys
    for (const auto& pair : keyNames) {
        if (pair.first == keyCode) {
            return pair.second;
        }
    }

    // Return hex code for unknown keys
    char buffer[16];
    sprintf_s(buffer, "Key 0x%02X", keyCode);
    return buffer;
}

// Start clicker threads
void startClickerThreads() {
    // Reset state
    isRunning = true;
    shouldClick = false;

    // Create clicking thread
    clickThread = std::thread(clickingThread);

    // Create status check thread
    uiThread = std::thread(statusCheckThread);
}

// Stop clicker threads
void stopClickerThreads() {
    // Signal threads to stop
    isRunning = false;

    // Wait for threads to finish
    if (clickThread.joinable()) {
        clickThread.join();
    }

    if (uiThread.joinable()) {
        uiThread.join();
    }
}

// Start the clicker
Napi::Value StartClicker(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    startClickerThreads();
    
    return Napi::Boolean::New(env, true);
}

// Stop the clicker
Napi::Value StopClicker(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    stopClickerThreads();
    
    return Napi::Boolean::New(env, true);
}

// Set click delay
Napi::Value SetClickDelay(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsNumber()) {
        Napi::TypeError::New(env, "Expected number argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    int delay = info[0].As<Napi::Number>().Int32Value();
    clickDelay.store(delay);
    
    return Napi::Number::New(env, delay);
}

// Get click delay
Napi::Value GetClickDelay(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Number::New(env, clickDelay.load());
}

// Toggle auto-click
Napi::Value ToggleAutoClick(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsBoolean()) {
        Napi::TypeError::New(env, "Expected boolean argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    bool enabled = info[0].As<Napi::Boolean>().Value();
    autoClickEnabled.store(enabled);
    
    return Napi::Boolean::New(env, enabled);
}

// Get auto-click state
Napi::Value GetAutoClickState(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Boolean::New(env, autoClickEnabled.load());
}

// Toggle butterfly mode
Napi::Value ToggleButterflyMode(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsBoolean()) {
        Napi::TypeError::New(env, "Expected boolean argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    bool enabled = info[0].As<Napi::Boolean>().Value();
    butterflyMode.store(enabled);
    
    return Napi::Boolean::New(env, enabled);
}

// Get butterfly mode state
Napi::Value GetButterflyMode(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Boolean::New(env, butterflyMode.load());
}

// Set butterfly keys
Napi::Value SetButterflyKeys(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 2 || !info[0].IsNumber() || !info[1].IsNumber()) {
        Napi::TypeError::New(env, "Expected two number arguments").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    int key1 = info[0].As<Napi::Number>().Int32Value();
    int key2 = info[1].As<Napi::Number>().Int32Value();
    
    butterflyKey1.store(key1);
    butterflyKey2.store(key2);
    
    return Napi::Boolean::New(env, true);
}

// Get butterfly keys
Napi::Value GetButterflyKeys(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    Napi::Object result = Napi::Object::New(env);
    result.Set("key1", Napi::Number::New(env, butterflyKey1.load()));
    result.Set("key2", Napi::Number::New(env, butterflyKey2.load()));
    result.Set("key1Name", Napi::String::New(env, getKeyName(butterflyKey1.load())));
    result.Set("key2Name", Napi::String::New(env, getKeyName(butterflyKey2.load())));
    
    return result;
}

// Set click key (for legacy mode)
Napi::Value SetClickKey(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    if (info.Length() < 1 || !info[0].IsNumber()) {
        Napi::TypeError::New(env, "Expected number argument").ThrowAsJavaScriptException();
        return env.Null();
    }
    
    int key = info[0].As<Napi::Number>().Int32Value();
    clickKey.store(key);
    
    return Napi::Number::New(env, key);
}

// Get click key (for legacy mode)
Napi::Value GetClickKey(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    Napi::Object result = Napi::Object::New(env);
    result.Set("key", Napi::Number::New(env, clickKey.load()));
    result.Set("keyName", Napi::String::New(env, getKeyName(clickKey.load())));
    
    return result;
}

// Get click counter
Napi::Value GetClickCounter(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Number::New(env, clickCounter.load());
}

// Reset click counter
Napi::Value ResetClickCounter(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    clickCounter.store(0);
    
    return Napi::Boolean::New(env, true);
}

// Check if Roblox is active
Napi::Value IsRobloxActive(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Boolean::New(env, isRobloxActive.load());
}

// Check if currently clicking
Napi::Value IsClicking(const Napi::CallbackInfo& info) {
    Napi::Env env = info.Env();
    
    return Napi::Boolean::New(env, shouldClick.load());
}

// Initialize the addon
Napi::Object Init(Napi::Env env, Napi::Object exports) {
    exports.Set(Napi::String::New(env, "startClicker"), Napi::Function::New(env, StartClicker));
    exports.Set(Napi::String::New(env, "stopClicker"), Napi::Function::New(env, StopClicker));
    exports.Set(Napi::String::New(env, "setClickDelay"), Napi::Function::New(env, SetClickDelay));
    exports.Set(Napi::String::New(env, "getClickDelay"), Napi::Function::New(env, GetClickDelay));
    exports.Set(Napi::String::New(env, "toggleAutoClick"), Napi::Function::New(env, ToggleAutoClick));
    exports.Set(Napi::String::New(env, "getAutoClickState"), Napi::Function::New(env, GetAutoClickState));
    exports.Set(Napi::String::New(env, "toggleButterflyMode"), Napi::Function::New(env, ToggleButterflyMode));
    exports.Set(Napi::String::New(env, "getButterflyMode"), Napi::Function::New(env, GetButterflyMode));
    exports.Set(Napi::String::New(env, "setButterflyKeys"), Napi::Function::New(env, SetButterflyKeys));
    exports.Set(Napi::String::New(env, "getButterflyKeys"), Napi::Function::New(env, GetButterflyKeys));
    exports.Set(Napi::String::New(env, "setClickKey"), Napi::Function::New(env, SetClickKey));
    exports.Set(Napi::String::New(env, "getClickKey"), Napi::Function::New(env, GetClickKey));
    exports.Set(Napi::String::New(env, "getClickCounter"), Napi::Function::New(env, GetClickCounter));
    exports.Set(Napi::String::New(env, "resetClickCounter"), Napi::Function::New(env, ResetClickCounter));
    exports.Set(Napi::String::New(env, "isRobloxActive"), Napi::Function::New(env, IsRobloxActive));
    exports.Set(Napi::String::New(env, "isClicking"), Napi::Function::New(env, IsClicking));
    return exports;
}

NODE_API_MODULE(cpp_addon, Init)
