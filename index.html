<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Blade Ball Master</title>
  <link rel="stylesheet" href="styles.css">
  <!-- Add Chinese fonts for more spectacular appearance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Ma+<PERSON>+<PERSON>&family=<PERSON>hi+<PERSON>g+Xing&family=ZCOOL+QingKe+HuangYou&family=ZCOOL+XiaoWei&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Custom title bar -->
  <div class="title-bar">
    <div class="title-bar-text">Blade Ball Master</div>
    <div class="window-controls">
      <div class="window-control-button" id="minimize-button">&#8211;</div>
      <div class="window-control-button" id="maximize-button">&#9744;</div>
      <div class="window-control-button" id="fullscreen-button">&#9974;</div>
      <div class="window-control-button window-control-close" id="close-button">&#10005;</div>
    </div>
  </div>

  <!-- Menu Bar -->
  <div class="menu-bar">
    <div class="menu-item active" id="menu-main">Main</div>
    <div class="menu-item" id="menu-settings">Settings</div>
  </div>

  <div class="container">
    <div class="sidebar">
      <!-- Vertical text for Ancient Warrior Phrase -->
      <div class="vertical-text top-text">
        <div class="letter">武</div>
        <div class="letter space"></div>
        <div class="letter">士</div>
        <div class="letter space"></div>
        <div class="letter">之</div>
        <div class="letter space"></div>
        <div class="letter">道</div>
        <div class="letter space"></div>
        <div class="letter">勇</div>
        <div class="letter space"></div>
        <div class="letter">往</div>
        <div class="letter space"></div>
        <div class="letter">直</div>
        <div class="letter space"></div>
        <div class="letter">前</div>
      </div>

      <!-- Logo with glow effect -->
      <div class="logo">
        <svg viewBox="0 0 100 100" width="60" height="60" class="logo-svg">
          <!-- Blade Ball logo - circular blade with motion lines -->
          <circle cx="50" cy="50" r="35" fill="none" stroke="currentColor" stroke-width="2" />
          <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4" stroke-dasharray="10 5" />
          <circle cx="50" cy="50" r="20" fill="currentColor" opacity="0.6" />
          <circle cx="50" cy="50" r="10" fill="currentColor" />
          <!-- Motion lines -->
          <path d="M20 50 L5 50" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M95 50 L80 50" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M50 20 L50 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M50 95 L50 80" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <!-- Diagonal motion lines -->
          <path d="M28 28 L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M82 28 L92 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M28 72 L18 82" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M82 72 L92 82" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="4" result="blur" />
            <feComposite in="SourceGraphic" in2="blur" operator="over" />
          </filter>
        </svg>
      </div>

      <!-- Vertical text for Ancient Warrior Phrase -->
      <div class="vertical-text bottom-text">
        <div class="letter">百</div>
        <div class="letter space"></div>
        <div class="letter">战</div>
        <div class="letter space"></div>
        <div class="letter">百</div>
        <div class="letter space"></div>
        <div class="letter">胜</div>
        <div class="letter space"></div>
        <div class="letter">不</div>
        <div class="letter space"></div>
        <div class="letter">败</div>
        <div class="letter space"></div>
        <div class="letter">之</div>
        <div class="letter space"></div>
        <div class="letter">将</div>
      </div>
    </div>

    <div class="main-content">
      <!-- Main Page -->
      <div class="page active" id="main-page">
        <div class="settings-grid">
          <div class="settings-section">
            <h2 class="settings-title">Auto Clicker</h2>
            <div class="setting-item">
              <div class="setting-header">
                <span>Auto Click</span>
                <div class="star-icon">★</div>
              </div>
              <div class="toggle-switch" id="auto-click-toggle">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Butterfly Mode</span>
                <div class="star-icon">★</div>
              </div>
              <div class="toggle-switch enabled" id="butterfly-mode-toggle">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Click Delay</span>
              </div>
              <div class="setting-value" id="click-delay-value">5ms</div>
              <div class="slider-container">
                <div class="slider" id="click-delay-slider">
                  <div class="slider-fill" style="width: 5%;"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h2 class="settings-title">Game Launcher</h2>

            <div class="setting-item">
              <div class="setting-header">
                <span>Launch Blade Ball</span>
                <div class="info-icon" title="Launch Blade Ball directly">ℹ</div>
              </div>
              <button class="control-button accent" id="launch-blade-ball">Launch Game</button>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Custom Game</span>
                <div class="info-icon" title="Enter a Roblox Place ID to launch">ℹ</div>
              </div>
              <div class="input-group">
                <input type="text" class="game-id-input" id="game-id-input" placeholder="Enter Place ID (e.g., 13772394625)">
                <button class="control-button" id="launch-custom-game">Launch</button>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Recent Games</span>
              </div>
              <div class="recent-games-list" id="recent-games-list">
                <!-- Recent games will be populated here -->
                <div class="empty-list-message">No recent games</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Page -->
      <div class="page" id="settings-page">
        <div class="settings-grid">
          <div class="settings-section">
            <h2 class="settings-title">Application Settings</h2>

            <div class="setting-item">
              <div class="setting-header">
                <span>Start with Windows</span>
              </div>
              <div class="toggle-switch" id="startup-toggle">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Minimize to Tray</span>
              </div>
              <div class="toggle-switch" id="tray-toggle">
                <div class="toggle-slider"></div>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Theme</span>
              </div>
              <select class="settings-select" id="theme-select">
                <option value="default">Default Purple</option>
                <option value="neon-purple">Neon Purple</option>
                <option value="ocean-blue">Ocean Blue</option>
                <option value="emerald-green">Emerald Green</option>
                <option value="amber-gold">Amber Gold</option>
              </select>
            </div>
          </div>

          <div class="settings-section">
            <h2 class="settings-title">Bloxstrap Integration</h2>

            <div class="setting-item">
              <div class="setting-header">
                <span>Bloxstrap Status</span>
              </div>
              <div class="status-indicator" id="bloxstrap-status">
                <div class="status-dot inactive"></div>
                <span class="status-text">Not Detected</span>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Ball Lag FFlags Integration</span>
                <div class="info-icon" title="Adds 100+ optimized Ball Lag FFlags to Bloxstrap's Fast Flag Editor for maximum performance">ℹ</div>
              </div>
              <div class="button-group">
                <button class="control-button" id="add-fflags-button" disabled>Add Ball Lag FFlags</button>
                <button class="control-button" id="remove-fflags-button" disabled>Remove Ball Lag FFlags</button>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Ball Lag FFlags Status</span>
              </div>
              <div class="fflag-status" id="fflag-status">
                <div class="fflag-item">
                  <span class="fflag-name">Ball Lag</span>
                  <span class="fflag-value" id="fflag-ball-lag">Not Set</span>
                </div>
                <div class="fflag-item">
                  <span class="fflag-name">DFFlagDebugPerfMode</span>
                  <span class="fflag-value" id="fflag-mouse-icon">Not Set</span>
                </div>
                <div class="fflag-item">
                  <span class="fflag-name">FFlagOptimizeNetwork</span>
                  <span class="fflag-value" id="fflag-collection-service">Not Set</span>
                </div>
                <div class="fflag-item">
                  <span class="fflag-name">DFIntTaskSchedulerTargetFps</span>
                  <span class="fflag-value" id="fflag-input-changed">Not Set</span>
                </div>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h2 class="settings-title">Keybinds</h2>

            <div class="setting-item">
              <div class="setting-header">
                <span>Toggle Auto Click</span>
              </div>
              <button class="keybind-button" id="toggle-click-keybind">F6</button>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Toggle Butterfly Mode</span>
              </div>
              <button class="keybind-button" id="toggle-butterfly-keybind">F7</button>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Increase Click Delay</span>
              </div>
              <button class="keybind-button" id="increase-delay-keybind">F8</button>
            </div>

            <div class="setting-item">
              <div class="setting-header">
                <span>Decrease Click Delay</span>
              </div>
              <button class="keybind-button" id="decrease-delay-keybind">F9</button>
            </div>
          </div>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
             <div class="click-counter-container">
              <span>Click Counter</span>
             </div>
            </div>
            <div class="counter-display">
              <div class="setting-value" id="click-counter">0</div>
              <div class="click-rate" id="click-rate">0 CPS</div>
            </div>
            <button class="reset-button" id="reset-counter">Reset</button>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Roblox Status</span>
            </div>
            <div class="status-indicator" id="roblox-status">
              <div class="status-dot inactive"></div>
              <span class="status-text">Inactive</span>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Click Status</span>
            </div>
            <div class="status-indicator" id="click-status">
              <div class="status-dot inactive"></div>
              <span class="status-text">Not Clicking</span>
            </div>
          </div>
        </div>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-header">
              <span>Butterfly Key 1</span>
              <div class="star-icon">★</div>
            </div>
            <div class="key-bind" id="butterfly-key1">F</div>
            <button class="bind-button" id="bind-key1">Bind</button>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Butterfly Key 2</span>
              <div class="star-icon">★</div>
            </div>
            <div class="key-bind" id="butterfly-key2">G</div>
            <button class="bind-button" id="bind-key2">Bind</button>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Legacy Click Key</span>
            </div>
            <div class="key-bind" id="click-key">Right Mouse</div>
            <button class="bind-button" id="bind-click-key">Bind</button>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Activation</span>
            </div>
            <div class="info-text">Press INSERT to toggle auto-click</div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Control</span>
            </div>
            <div class="button-group">
              <button class="control-button" id="start-button">Start</button>
              <button class="control-button" id="stop-button">Stop</button>
            </div>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <span>Information</span>
            </div>
            <div class="info-text">
              Butterfly mode uses two keys for rapid clicking.<br>
              Legacy mode uses hold-to-click with one key.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="renderer.js"></script>
  <script>
    // Window control buttons functionality using direct require
    const remote = require('@electron/remote');

    document.getElementById('minimize-button').addEventListener('click', () => {
      remote.getCurrentWindow().minimize();
    });

    document.getElementById('maximize-button').addEventListener('click', () => {
      const win = remote.getCurrentWindow();
      if (win.isMaximized()) {
        win.unmaximize();
      } else {
        win.maximize();
      }
    });

    document.getElementById('close-button').addEventListener('click', () => {
      remote.getCurrentWindow().close();
    });

    document.getElementById('fullscreen-button').addEventListener('click', () => {
      const win = remote.getCurrentWindow();
      win.setFullScreen(!win.isFullScreen());
    });
  </script>
</body>
</html>
