<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Loading</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #0d0d12 0%, #1a1a24 50%, #0d0d12 100%);
      display: flex;
      flex-direction: column;
      height: 100vh;
      overflow: hidden;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      position: relative;
    }

    /* Animated background particles */
    .particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }

    .particle {
      position: absolute;
      background: radial-gradient(circle, #6c5ce7 0%, transparent 70%);
      border-radius: 50%;
      animation: float 6s infinite ease-in-out;
      opacity: 0.3;
    }

    .particle:nth-child(1) { width: 4px; height: 4px; left: 10%; animation-delay: 0s; animation-duration: 8s; }
    .particle:nth-child(2) { width: 6px; height: 6px; left: 20%; animation-delay: 1s; animation-duration: 7s; }
    .particle:nth-child(3) { width: 3px; height: 3px; left: 30%; animation-delay: 2s; animation-duration: 9s; }
    .particle:nth-child(4) { width: 5px; height: 5px; left: 40%; animation-delay: 0.5s; animation-duration: 6s; }
    .particle:nth-child(5) { width: 4px; height: 4px; left: 50%; animation-delay: 1.5s; animation-duration: 8s; }
    .particle:nth-child(6) { width: 7px; height: 7px; left: 60%; animation-delay: 3s; animation-duration: 7s; }
    .particle:nth-child(7) { width: 3px; height: 3px; left: 70%; animation-delay: 2.5s; animation-duration: 9s; }
    .particle:nth-child(8) { width: 5px; height: 5px; left: 80%; animation-delay: 4s; animation-duration: 6s; }
    .particle:nth-child(9) { width: 4px; height: 4px; left: 90%; animation-delay: 1s; animation-duration: 8s; }
    .particle:nth-child(10) { width: 6px; height: 6px; left: 15%; animation-delay: 3.5s; animation-duration: 7s; }

    @keyframes float {
      0%, 100% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 0.3;
      }
      90% {
        opacity: 0.3;
      }
      50% {
        transform: translateY(-10vh) rotate(180deg);
        opacity: 0.6;
      }
    }

    /* Enhanced title bar styles */
    .title-bar {
      height: 30px;
      background: linear-gradient(90deg, #0f0f1e 0%, #1a1a2e 50%, #0f0f1e 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      -webkit-app-region: drag;
      user-select: none;
      padding: 0 10px;
      border-bottom: 1px solid rgba(108, 92, 231, 0.2);
      backdrop-filter: blur(10px);
      z-index: 10;
      position: relative;
    }

    .title-bar-text {
      color: #e6e6e6;
      font-size: 12px;
      margin-left: 5px;
    }

    .window-controls {
      display: flex;
      -webkit-app-region: no-drag; /* Makes the buttons clickable */
    }

    .window-control-button {
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #e6e6e6;
      font-size: 14px;
      cursor: pointer;
    }

    .window-control-button:hover {
      background-color: #2d2d4d;
    }

    .window-control-close:hover {
      background-color: #e81123;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 5;
      position: relative;
      animation: fadeInUp 1s ease-out;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .logo {
      width: 140px;
      height: 140px;
      margin-bottom: 40px;
      position: relative;
      animation: logoEntrance 2s ease-out;
    }

    @keyframes logoEntrance {
      0% {
        opacity: 0;
        transform: scale(0.3) rotate(-180deg);
      }
      50% {
        opacity: 0.7;
        transform: scale(1.1) rotate(0deg);
      }
      100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
      }
    }

    .logo-svg {
      color: #6c5ce7;
      animation: logoSpin 8s infinite linear, logoPulse 3s infinite ease-in-out;
      filter: drop-shadow(0 0 10px rgba(108, 92, 231, 0.8))
              drop-shadow(0 0 20px rgba(108, 92, 231, 0.6))
              drop-shadow(0 0 30px rgba(108, 92, 231, 0.4));
      transition: all 0.3s ease;
    }

    .logo:hover .logo-svg {
      transform: scale(1.1);
      filter: drop-shadow(0 0 15px rgba(108, 92, 231, 1))
              drop-shadow(0 0 30px rgba(108, 92, 231, 0.8))
              drop-shadow(0 0 45px rgba(108, 92, 231, 0.6));
    }

    @keyframes logoSpin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes logoPulse {
      0%, 100% {
        filter: drop-shadow(0 0 10px rgba(108, 92, 231, 0.8))
                drop-shadow(0 0 20px rgba(108, 92, 231, 0.6))
                drop-shadow(0 0 30px rgba(108, 92, 231, 0.4));
      }
      50% {
        filter: drop-shadow(0 0 20px rgba(108, 92, 231, 1))
                drop-shadow(0 0 40px rgba(108, 92, 231, 0.8))
                drop-shadow(0 0 60px rgba(108, 92, 231, 0.6));
      }
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    .loading-text {
      color: #e6e6e6;
      font-size: 28px;
      font-weight: 300;
      margin-bottom: 30px;
      letter-spacing: 4px;
      text-transform: uppercase;
      animation: textGlow 2s infinite ease-in-out, textSlideIn 1s ease-out;
      text-shadow: 0 0 10px rgba(230, 230, 230, 0.5);
    }

    @keyframes textGlow {
      0%, 100% {
        text-shadow: 0 0 10px rgba(230, 230, 230, 0.5), 0 0 20px rgba(108, 92, 231, 0.3);
      }
      50% {
        text-shadow: 0 0 20px rgba(230, 230, 230, 0.8), 0 0 30px rgba(108, 92, 231, 0.6);
      }
    }

    @keyframes textSlideIn {
      from {
        opacity: 0;
        transform: translateY(20px);
        letter-spacing: 8px;
      }
      to {
        opacity: 1;
        transform: translateY(0);
        letter-spacing: 4px;
      }
    }

    .loading-status {
      color: #8a8a9a;
      font-size: 14px;
      margin-bottom: 25px;
      letter-spacing: 1px;
      animation: statusFade 3s infinite ease-in-out;
      min-height: 20px;
      text-align: center;
    }

    @keyframes statusFade {
      0%, 100% { opacity: 0.6; }
      50% { opacity: 1; }
    }

    .loading-dots {
      display: flex;
      gap: 12px;
      margin-bottom: 30px;
    }

    .dot {
      width: 14px;
      height: 14px;
      background: radial-gradient(circle, #6c5ce7 0%, #8a7eeb 100%);
      border-radius: 50%;
      animation: dotBounce 1.6s infinite ease-in-out both;
      box-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
    }

    .dot:nth-child(1) { animation-delay: -0.32s; }
    .dot:nth-child(2) { animation-delay: -0.16s; }
    .dot:nth-child(3) { animation-delay: 0s; }

    @keyframes dotBounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
        box-shadow: 0 0 5px rgba(108, 92, 231, 0.3);
      }
      40% {
        transform: scale(1.2);
        opacity: 1;
        box-shadow: 0 0 20px rgba(108, 92, 231, 0.8);
      }
    }

    .progress-bar {
      width: 350px;
      height: 8px;
      background: linear-gradient(90deg, #1a1a24 0%, #2a2a36 50%, #1a1a24 100%);
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(108, 92, 231, 0.2);
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #6c5ce7 0%, #8a7eeb 50%, #a855f7 100%);
      border-radius: 4px;
      width: 0%;
      animation: progressFill 4s forwards cubic-bezier(0.19, 1, 0.22, 1);
      position: relative;
      box-shadow: 0 0 10px rgba(108, 92, 231, 0.6);
    }

    .progress-fill::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
      animation: progressShimmer 2s infinite;
    }

    @keyframes progressFill {
      0% { width: 0%; }
      25% { width: 30%; }
      50% { width: 60%; }
      75% { width: 85%; }
      100% { width: 100%; }
    }

    @keyframes progressShimmer {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    .progress-percentage {
      color: #8a8a9a;
      font-size: 12px;
      margin-top: 10px;
      letter-spacing: 1px;
      animation: percentageFade 0.5s ease-in-out;
    }

    @keyframes percentageFade {
      from { opacity: 0; transform: translateY(5px); }
      to { opacity: 1; transform: translateY(0); }
    }

    /* Version info styling */
    .version-info {
      position: absolute;
      bottom: 20px;
      right: 20px;
      color: #5a5a6a;
      font-size: 11px;
      letter-spacing: 0.5px;
      z-index: 10;
    }

    .copyright {
      position: absolute;
      bottom: 20px;
      left: 20px;
      color: #5a5a6a;
      font-size: 11px;
      letter-spacing: 0.5px;
      z-index: 10;
    }
  </style>
</head>
<body>
  <!-- Custom title bar -->
  <div class="title-bar">
    <div class="title-bar-text">Blade Ball Master</div>
    <div class="window-controls">
      <div class="window-control-button" id="minimize-button">&#8211;</div>
      <div class="window-control-button" id="maximize-button">&#9744;</div>
      <div class="window-control-button window-control-close" id="close-button">&#10005;</div>
    </div>
  </div>

  <!-- Animated background particles -->
  <div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>

  <!-- Main content -->
  <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
    <div class="loading-container">
      <div class="logo">
        <svg viewBox="0 0 100 100" width="140" height="140" class="logo-svg">
          <!-- Enhanced Blade Ball logo -->
          <circle cx="50" cy="50" r="35" fill="none" stroke="currentColor" stroke-width="2" />
          <circle cx="50" cy="50" r="30" fill="none" stroke="currentColor" stroke-width="4" stroke-dasharray="10 5" />
          <circle cx="50" cy="50" r="20" fill="currentColor" opacity="0.6" />
          <circle cx="50" cy="50" r="10" fill="currentColor" />
          <!-- Motion lines -->
          <path d="M20 50 L5 50" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M95 50 L80 50" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M50 20 L50 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M50 95 L50 80" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <!-- Diagonal motion lines -->
          <path d="M28 28 L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M82 28 L92 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M28 72 L18 82" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
          <path d="M82 72 L92 82" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
        </svg>
      </div>

      <div class="loading-text">BLADE BALL MASTER</div>
      <div class="loading-status" id="loading-status">Initializing...</div>

      <div class="loading-dots">
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
      </div>

      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
      <div class="progress-percentage" id="progress-percentage">0%</div>
    </div>
  </div>

  <!-- Footer info -->
  <div class="copyright">© 2024 Blade Ball Master</div>
  <div class="version-info">v1.0.0</div>

  <script>
    // Enhanced loading simulation with dynamic messages and progress
    const loadingStatus = document.getElementById('loading-status');
    const progressPercentage = document.getElementById('progress-percentage');

    const loadingSteps = [
      { message: 'Initializing...', duration: 500 },
      { message: 'Loading core modules...', duration: 600 },
      { message: 'Setting up auto-clicker engine...', duration: 700 },
      { message: 'Configuring user interface...', duration: 500 },
      { message: 'Loading themes and styles...', duration: 400 },
      { message: 'Initializing keybind system...', duration: 500 },
      { message: 'Preparing Blade Ball integration...', duration: 600 },
      { message: 'Finalizing setup...', duration: 400 },
      { message: 'Ready to launch!', duration: 300 }
    ];

    let currentStep = 0;
    let totalProgress = 0;
    const totalSteps = loadingSteps.length;

    function updateLoadingStep() {
      if (currentStep < loadingSteps.length) {
        const step = loadingSteps[currentStep];
        loadingStatus.textContent = step.message;

        // Calculate progress
        totalProgress = ((currentStep + 1) / totalSteps) * 100;
        progressPercentage.textContent = `${Math.round(totalProgress)}%`;

        // Add animation class for status update
        loadingStatus.style.animation = 'none';
        loadingStatus.offsetHeight; // Trigger reflow
        loadingStatus.style.animation = 'statusFade 0.5s ease-in-out';

        currentStep++;

        setTimeout(updateLoadingStep, step.duration);
      } else {
        // Loading complete, redirect to main app
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 500);
      }
    }

    // Start loading sequence
    setTimeout(updateLoadingStep, 800);

    // Window control buttons functionality using direct require
    const remote = require('@electron/remote');

    document.getElementById('minimize-button').addEventListener('click', () => {
      remote.getCurrentWindow().minimize();
    });

    document.getElementById('maximize-button').addEventListener('click', () => {
      const win = remote.getCurrentWindow();
      if (win.isMaximized()) {
        win.unmaximize();
      } else {
        win.maximize();
      }
    });

    document.getElementById('close-button').addEventListener('click', () => {
      remote.getCurrentWindow().close();
    });
  </script>
</body>
</html>
