const { app, BrowserWindow, ipcMain, shell } = require('electron');
const path = require('path');
const fs = require('fs');

require('@electron/remote/main').initialize();

const originalConsoleLog = console.log;

console.log = function() {
  const args = Array.from(arguments);

  const message = args.join(' ');
  if (message.includes('Window Title:') || message.includes('Class Name:')) {
    return;
  } else {
    originalConsoleLog.apply(console, args);
  }
};

app.whenReady().then(() => {
  console.log('App is ready');
  console.log('App path:', app.getAppPath());
  console.log('User data path:', app.getPath('userData'));
  console.log('Exe path:', app.getPath('exe'));
  console.log('Resources path:', process.resourcesPath || 'Not available');
});

let mainWindow;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1600,
    height: 900,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      preload: path.join(__dirname, 'preload.js'),
      zoomFactor: 0.9,
      disableBlinkFeatures: 'MouseWheelTextZoom'
    },
    backgroundColor: '#1a1a2e',
    autoHideMenuBar: true,
    frame: false,
    titleBarStyle: 'hidden',
    transparent: false,
    minWidth: 1600,
    minHeight: 900,
    resizable: false,
    fullscreenable: true
  });

  require('@electron/remote/main').enable(mainWindow.webContents);

  mainWindow.loadFile('loading.html');

  mainWindow.webContents.on('did-finish-load', () => {
    mainWindow.webContents.setZoomFactor(0.9);

    mainWindow.webContents.on('zoom-changed', (event) => {
      event.preventDefault();
      mainWindow.webContents.setZoomFactor(0.9);
    });
  });

  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  createWindow();

  const { globalShortcut } = require('electron');

  globalShortcut.register('CommandOrControl+=', () => { return false; });
  globalShortcut.register('CommandOrControl+-', () => { return false; });
  globalShortcut.register('CommandOrControl+0', () => { return false; });

  globalShortcut.register('CommandOrControl+numadd', () => { return false; });
  globalShortcut.register('CommandOrControl+numsub', () => { return false; });
});

app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function () {
  if (mainWindow === null) createWindow();
});

const RECENT_GAMES_FILE = path.join(app.getPath('userData'), 'recent-games.json');

function getRecentGames() {
  try {
    if (fs.existsSync(RECENT_GAMES_FILE)) {
      const data = fs.readFileSync(RECENT_GAMES_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error reading recent games:', error);
  }
  return [];
}

function saveRecentGame(gameId, gameName) {
  try {
    const recentGames = getRecentGames();

    const existingIndex = recentGames.findIndex(game => game.id === gameId);
    if (existingIndex !== -1) {
      const game = recentGames.splice(existingIndex, 1)[0];
      game.lastPlayed = new Date().toISOString();
      recentGames.unshift(game);
    } else {
      recentGames.unshift({
        id: gameId,
        name: gameName || `Game ${gameId}`,
        lastPlayed: new Date().toISOString()
      });
    }

    const trimmedList = recentGames.slice(0, 10);

    fs.writeFileSync(RECENT_GAMES_FILE, JSON.stringify(trimmedList, null, 2), 'utf8');
    return trimmedList;
  } catch (error) {
    console.error('Error saving recent game:', error);
    return [];
  }
}

function launchRobloxGame(placeId) {
  try {
    console.log(`Attempting to launch game with Place ID: ${placeId}`);

    console.log("Using Roblox URI protocol");
    shell.openExternal(`roblox://experiences/start?placeId=${placeId}`);

    return { success: true };
  } catch (error) {
    console.error('Error launching game:', error);
    return { success: false, error: error.message };
  }
}

ipcMain.handle('get-recent-games', () => {
  return getRecentGames();
});

ipcMain.handle('launch-game', async (_, { placeId, gameName }) => {
  const result = launchRobloxGame(placeId);

  if (result.success) {
    saveRecentGame(placeId, gameName);
  }

  return result;
});

const BLADE_BALL_PLACE_ID = '13772394625';

ipcMain.handle('launch-blade-ball', () => {
  const result = launchRobloxGame(BLADE_BALL_PLACE_ID);

  if (result.success) {
    saveRecentGame(BLADE_BALL_PLACE_ID, 'Blade Ball');
  }

  return result;
});
