{"name": "roblox-auto-clicker", "version": "1.0.0", "description": "Blade Ball Master with C++ integration", "main": "main.js", "scripts": {"start": "electron .", "rebuild": "electron-rebuild", "install": "node-gyp rebuild", "postinstall": "electron-builder install-app-deps", "prebuild": "electron-rebuild", "build": "electron-builder build --win", "dist": "electron-builder", "version:patch": "npm version patch", "version:minor": "npm version minor", "version:major": "npm version major", "release:patch": "npm run version:patch && git push && git push --tags", "release:minor": "npm run version:minor && git push && git push --tags", "release:major": "npm run version:major && git push && git push --tags"}, "keywords": ["electron", "c++", "game", "settings"], "author": "", "license": "MIT", "devDependencies": {"electron": "^28.3.3", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9"}, "dependencies": {"@electron/remote": "^2.1.2", "axios": "^1.9.0", "bindings": "^1.5.0", "express-rate-limit": "^7.5.0", "install": "^0.13.0", "noblox.js": "^6.2.0", "node-addon-api": "^7.0.0", "nodeuse": "^0.0.0", "npm": "^11.4.1", "sanitize-html": "^2.17.0", "winston": "^3.17.0"}, "gypfile": true, "build": {"appId": "com.roblox.autoclicker", "productName": "Blade Ball Master", "win": {"target": ["portable", "nsis"]}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": ["build/**"], "asarUnpack": ["**/*.node"]}}