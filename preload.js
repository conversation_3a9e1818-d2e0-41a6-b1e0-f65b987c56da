const path = require('path');
const fs = require('fs');

function findAddon() {
  const isPackaged = process.mainModule && process.mainModule.filename.indexOf('app.asar') !== -1;

  let possiblePaths = [
    './build/Release/cpp_addon.node',
    './build/Debug/cpp_addon.node',
    path.join(__dirname, 'build/Release/cpp_addon.node'),
    path.join(__dirname, 'build/Debug/cpp_addon.node'),

    path.join(process.resourcesPath, 'build/Release/cpp_addon.node'),
    path.join(process.resourcesPath, 'build/Debug/cpp_addon.node'),
    path.join(process.resourcesPath, 'app.asar.unpacked/build/Release/cpp_addon.node'),
    path.join(process.resourcesPath, 'app.asar.unpacked/build/Debug/cpp_addon.node')
  ];

  console.log('Searching for addon in the following locations:');
  for (const addonPath of possiblePaths) {
    try {
      console.log(`Checking path: ${addonPath}`);
      if (fs.existsSync(addonPath)) {
        console.log(`Found addon at: ${addonPath}`);
        return require(addonPath);
      }
    } catch (err) {
      console.log(`Error checking path ${addonPath}:`, err.message);
    }
  }

  return null;
}

window.addEventListener('DOMContentLoaded', () => {
  console.log('Preload script running...');

  const { webFrame } = require('electron');

  webFrame.setVisualZoomLevelLimits(1, 1);

  webFrame.setZoomFactor(0.9);
  webFrame.setZoomLevel(0);

  window.versions = {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  };

  window.nodePath = path;
  window.nodeFs = fs;

  try {
    const cppAddon = findAddon();

    if (cppAddon) {
      console.log('Successfully loaded C++ addon');
      window.cppAddon = cppAddon;
    } else {
      throw new Error('Could not find C++ addon in any expected location');
    }
  } catch (error) {
    console.error('Failed to load C++ addon:', error);
    console.log('Falling back to JavaScript implementation');

    console.log('Creating mock C++ addon for UI testing');
    window.cppAddon = {
      startClicker: () => console.log('Mock: Start clicker'),
      stopClicker: () => console.log('Mock: Stop clicker'),
      setClickDelay: (delay) => console.log(`Mock: Set click delay to ${delay}ms`),
      getClickDelay: () => 5,
      toggleAutoClick: (enabled) => console.log(`Mock: Auto click ${enabled ? 'enabled' : 'disabled'}`),
      getAutoClickState: () => false,
      toggleButterflyMode: (enabled) => console.log(`Mock: Butterfly mode ${enabled ? 'enabled' : 'disabled'}`),
      getButterflyMode: () => true,
      setButterflyKeys: (key1, key2) => console.log(`Mock: Set butterfly keys to ${key1} and ${key2}`),
      getButterflyKeys: () => ({ key1: 70, key2: 71, key1Name: 'F', key2Name: 'G' }),
      setClickKey: (key) => console.log(`Mock: Set click key to ${key}`),
      getClickKey: () => ({ key: 2, keyName: 'Right Mouse' }),
      getClickCounter: () => Math.floor(Math.random() * 1000),
      resetClickCounter: () => console.log('Mock: Reset click counter'),
      isRobloxActive: () => Math.random() > 0.5,
      isClicking: () => Math.random() > 0.7
    };
  }

  console.log('Preload script completed');
});
