document.addEventListener('DOMContentLoaded', () => {
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
      e.preventDefault();
      return false;
    }

    if (e.key === 'F11') {
      const remote = require('@electron/remote');
      const win = remote.getCurrentWindow();
      win.setFullScreen(!win.isFullScreen());
      e.preventDefault();
    }
  });

  const { ipcRenderer } = require('electron');
  const launchBladeButton = document.getElementById('launch-blade-ball');
  const gameIdInput = document.getElementById('game-id-input');
  const launchCustomGameButton = document.getElementById('launch-custom-game');
  const recentGamesList = document.getElementById('recent-games-list');

  async function loadRecentGames() {
    try {
      const recentGames = await ipcRenderer.invoke('get-recent-games');

      recentGamesList.innerHTML = '';

      if (recentGames.length === 0) {
        recentGamesList.innerHTML = '<div class="empty-list-message">No recent games</div>';
        return;
      }

      recentGames.forEach(game => {
        const gameItem = document.createElement('div');
        gameItem.className = 'recent-game-item';
        gameItem.dataset.id = game.id;

        gameItem.innerHTML = `
          <div class="game-info">
            <div class="game-name">${game.name}</div>
            <div class="game-id">${game.id}</div>
          </div>
          <div class="launch-icon">▶</div>
        `;

        gameItem.addEventListener('click', () => {
          launchGame(game.id, game.name);
        });

        recentGamesList.appendChild(gameItem);
      });
    } catch (error) {
      console.error('Error loading recent games:', error);
      recentGamesList.innerHTML = '<div class="empty-list-message">Error loading games</div>';
    }
  }

  launchBladeButton.addEventListener('click', async () => {
    try {
      launchBladeButton.textContent = 'Launching...';
      launchBladeButton.disabled = true;

      const result = await ipcRenderer.invoke('launch-blade-ball');

      if (result.success) {
        showNotification('Launching Blade Ball...', 'success');
        loadRecentGames();
      } else {
        showNotification(`Error: ${result.error}`, 'error');
      }
    } catch (error) {
      showNotification(`Error: ${error.message}`, 'error');
    } finally {
      launchBladeButton.textContent = 'Launch Game';
      launchBladeButton.disabled = false;
    }
  });

  launchCustomGameButton.addEventListener('click', () => {
    const placeId = gameIdInput.value.trim();

    if (!placeId) {
      showNotification('Please enter a valid Place ID', 'warning');
      return;
    }

    launchGame(placeId);
  });

  async function launchGame(placeId, gameName) {
    try {
      launchCustomGameButton.textContent = 'Launching...';
      launchCustomGameButton.disabled = true;

      const result = await ipcRenderer.invoke('launch-game', {
        placeId,
        gameName: gameName || `Game ${placeId}`
      });

      if (result.success) {
        showNotification(`Launching game ${placeId}...`, 'success');
        gameIdInput.value = '';
        loadRecentGames();
      } else {
        showNotification(`Error: ${result.error}`, 'error');
      }
    } catch (error) {
      showNotification(`Error: ${error.message}`, 'error');
    } finally {
      launchCustomGameButton.textContent = 'Launch';
      launchCustomGameButton.disabled = false;
    }
  }

  function showNotification(message, type = 'info') {
    console.log(`[${type}] ${message}`);

    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  }

  loadRecentGames();

  const menuMain = document.getElementById('menu-main');
  const menuSettings = document.getElementById('menu-settings');
  const mainPage = document.getElementById('main-page');
  const settingsPage = document.getElementById('settings-page');
  const themeSelect = document.getElementById('theme-select');

  function setTheme(themeName) {
    document.body.removeAttribute('data-theme');

    if (themeName !== 'default') {
      document.body.setAttribute('data-theme', themeName);
    }

    localStorage.setItem('theme', themeName);
  }

  function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'default';
    setTheme(savedTheme);
    themeSelect.value = savedTheme;
  }

  themeSelect.addEventListener('change', () => {
    setTheme(themeSelect.value);
  });

  initTheme();

  const bloxstrapStatus = document.getElementById('bloxstrap-status');
  const addFFlagsButton = document.getElementById('add-fflags-button');
  const removeFFlagsButton = document.getElementById('remove-fflags-button');
  const fflagBallLag = document.getElementById('fflag-ball-lag');
  const fflagMouseIcon = document.getElementById('fflag-mouse-icon');
  const fflagCollectionService = document.getElementById('fflag-collection-service');
  const fflagInputChanged = document.getElementById('fflag-input-changed');
  const autoClickerFFlags = {
    "FLogNetwork": "7",
    "FFlagHandleAltEnterFullscreenManually": "False",
    "DFFlagBaseNetworkMetrics": "False",
    "DFFlagBrowserTrackerIdTelemetryEnabled": "False",
    "DFFlagDebugPauseVoxelizer": "True",
    "DFFlagDebugPerfMode": "True",
    "DFFlagEnableLightstepReporting2": "False",
    "DFIntClientLightingTechnologyChangedTelemetryHundredthsPercent": "0",
    "DFIntConnectionMTUSize": "900",
    "DFIntGraphicsLevel": "4",
    "DFIntLightstepHTTPTransportHundredthsPercent2": "0",
    "DFIntMaxAcceptableUpdateDelay": "1",
    "DFIntMaxDataOutJobScaling": "**********",
    "DFIntMaxFrameBufferSize": "1",
    "DFIntMaxFramesToSend": "-1",
    "DFIntMaxThrottleCount": "1",
    "DFIntNetworkLatencyTolerance": "1",
    "DFIntNetworkPrediction": "120",
    "DFIntNewRunningBaseAltitudeD": "195",
    "DFIntOptimizePingThreshold": "50",
    "DFIntPlayerNetworkUpdateQueueSize": "20",
    "DFIntPlayerNetworkUpdateRate": "60",
    "DFIntRunningBaseOrientationP": "450",
    "DFIntS2PhysicsSenderRate": "250",
    "DFIntServerPhysicsUpdateRate": "60",
    "DFIntServerTickRate": "60",
    "DFStringAltHttpPointsReporterUrl": "null",
    "DFStringAltTelegrafHTTPTransportUrl": "null",
    "DFStringCrashUploadToBacktraceBaseUrl": "null",
    "DFStringCrashUploadToBacktraceWindowsPlayerToken": "null",
    "DFStringCrachUploadToBacktraceMacPlayerToken": "null",
    "DFStringHttpPointsReporterUrl": "null",
    "DFStringLightstepHTTPTransportUrlHost": "null",
    "DFStringLightstepHTTPTransportUrlPath": "null",
    "DFStringLightstepToken": "null",
    "DFStringRobloxAnalyticsURL": "null",
    "DFStringTelegrafHTTPTransportUrl": "null",
    "DFStringTelemetryV2Url": "null",
    "FFlagAdServiceEnabled": "False",
    "FFlagCommitToGraphicsQualityFix": "True",
    "FFlagDebugDisableOTAMaterialTexture": "True",
    "FFlagDebugDisableTelemetryEphemeralCounter": "True",
    "FFlagDebugDisableTelemetryEphemeralStat": "True",
    "FFlagDebugDisableTelemetryEventIngest": "True",
    "FFlagDebugDisableTelemetryPoint": "True",
    "FFlagDebugDisableTelemetryV2Counter": "True",
    "FFlagDebugDisableTelemetryV2Event": "True",
    "FFlagDebugDisableTelemetryV2Stat": "True",
    "FFlagDebugDisplayFPS": "False",
    "FFlagDebugGraphicsDisableDirect3D11": "False",
    "FFlagDebugSkyGray": "True",
    "FFlagDisablePostFx": "True",
    "FFlagEnableInGameMenuChrome": "True",
    "FFlagFastGPULightCulling3": "True",
    "FFlagFastLoadingAssets": "True",
    "FFlagGraphicsEnableD3D10Compute": "True",
    "FFlagLuaAppSystemBar": "False",
    "FFlagMSRefactor5": "False",
    "FFlagNewLightAttenuation": "True",
    "FFlagOptimizeAnimations": "True",
    "FFlagOptimizeNetwork": "True",
    "FFlagOptimizeNetworkRouting": "True",
    "FFlagOptimizeNetworkTransport": "True",
    "FFlagOptimizeServerTickRate": "True",
    "FFlagPreloadAllFonts": "True",
    "FFlagReduceRenderDistance": "True",
    "FIntFontSizePadding": "3",
    "FIntFRMMaxGrassDistance": "0",
    "FIntPGSAngularDampingPermillPersecond": "*********",
    "FIntRakNetResendBufferArrayLength": "128",
    "FIntRenderGrassDetailStrands": "0",
    "FIntRenderGrassHeightScaler": "0",
    "FIntRenderShafowIntensity": "0",
    "FIntRenderShadowmapBias": "0",
    "FIntScrollWheelDeltaAmount": "500",
    "FStringCoreScriptBacktraceErrorUploadToken": "null",
    "FStringGamesUrlPath": "/games/",
    "DFIntTaskSchedulerTargetFps": "9999",
    "FStringPartTexturePackTablePre2022": "{\u0022foil\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[238,238,238,255]},\u0022asphalt\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[227,227,228,234]},\u0022basalt\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[160,160,158,238]},\u0022brick\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[229,214,205,227]},\u0022cobblestone\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[218,219,219,243]},\u0022concrete\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[225,225,224,255]},\u0022crackedlava\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[76,79,81,156]},\u0022diamondplate\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[210,210,210,255]},\u0022fabric\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[221,221,221,255]},\u0022glacier\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[225,229,229,243]},\u0022glass\u0022:{\u0022ids\u0022:[\u0022rbxassetid://9873284556\u0022,\u0022rbxassetid://9438453972\u0022],\u0022color\u0022:[254,254,254,7]},\u0022granite\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[210,206,200,255]},\u0022grass\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[196,196,189,241]},\u0022ground\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[165,165,160,240]},\u0022ice\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[235,239,241,248]},\u0022leafygrass\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[182,178,175,234]},\u0022limestone\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[250,248,243,250]},\u0022marble\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[181,183,193,249]},\u0022metal\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[226,226,226,255]},\u0022mud\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[193,192,193,252]},\u0022pavement\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[218,218,219,236]},\u0022pebble\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[204,203,201,234]},\u0022plastic\u0022:{\u0022ids\u0022:[\u0022\u0022,\u0022rbxassetid://9475422736\u0022],\u0022color\u0022:[255,255,255,255]},\u0022rock\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[211,211,210,248]},\u0022corrodedmetal\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[206,177,163,180]},\u0022salt\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[249,249,249,255]},\u0022sand\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[218,216,210,240]},\u0022sandstone\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[241,234,230,246]},\u0022slate\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[235,234,235,254]},\u0022snow\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[239,240,240,255]},\u0022wood\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[217,209,208,255]},\u0022woodplanks\u0022:{\u0022ids\u0022:[\u0022rbxassetid://***********\u0022,\u0022rbxassetid://*********08\u0022],\u0022color\u0022:[207,208,206,254]}}",
    "DFIntWaitOnUpdateNetworkLoopEndedMS": "100",
    "FFlagEnableMenuControlsABTest": "False",
    "FFlagEnableV3MenuABTest3": "False",
    "FIntRenderShadowIntensity": "0",
    "DFIntCodecMaxIncomingPackets": "-**********",
    "DFIntMaxAverageFrameDelayExceedFactor": "0",
    "DFIntLargePacketQueueSizeCutoffMB": "1000",
    "FFlagEnableInGameMenuChromeABTest3": "False",
    "FIntInterpolationAwareTargetTimeLerpHundredth": "100",
    "FFlagEnableChromePinnedChat": "True",
    "FIntFullscreenTitleBarTriggerDelayMillis": "3600000",
    "DFIntMaxProcessPacketsStepsPerCyclic": "-**********",
    "DFFlagDebugRenderForceTechnologyVoxel": "True",
    "DFIntCodecMaxOutgoingFrames": "-**********",
    "FFlagDisableNewIGMinDUA": "True",
    "FIntDebugTextureManagerSkipMips": "0",
    "FFlagEnableInGameMenuModernization": "True",
    "DFIntRakNetResendRttMultiple": "1",
    "FFlagEnableInGameMenuControls": "True",
    "DFIntRaknetBandwidthPingSendEveryXSeconds": "1",
    "DFIntMaxProcessPacketsJobScaling": "-**********",
    "DFIntMinimalNetworkPrediction": "1",
    "DFIntMaxProcessPacketsStepsAccumulated": "0",
    "DFIntRaknetBandwidthInfluxHundredthsPercentageV2": "10000",
    "FFlagTaskSchedulerLimitTargetFpsTo2402": "False",
    "DFIntWaitOnRecvFromLoopEndedMS": "100",
    "DFIntRakNetLoopMs": "1",
    "DFIntMegaReplicatorNetworkQualityProcessorUnit": "10",
    "DFIntRakNetApplicationFeedbackScaleUpFactorHundredthPercent": "100",
    "DFIntClientPacketHealthyAllocationPercent": "50",
    "DFIntClientPacketMaxDelayMs": "-10",
    "DFIntRakNetSelectUnblockSocketWriteDurationMs": "10",
    "FFlagDebugGraphicsPreferD3D11": "True",
    "FFlagFixGraphicsQuality": "True",
    "DFIntRakNetMinAckGrowthPercent": "100",
    "DFIntInterpolationNumMechanismsPerTask": "100",
    "DFIntNumFramesAllowedToBeAboveError": "0",
    "DFIntRakNetClockDriftAdjustmentPerPingMillisecond": "-**********",
    "DFIntRuntimeTickrate": "**********",
    "DFIntParallelAdaptiveInterpolationBatchCount": "1",
    "DFIntVoiceChatRollOffMaxDistance": "300",
    "FIntMaquettesFrameRateBufferPercentage": "1",
    "FIntInterpolationMaxDelayMSec": "-1000",
    "FIntRakNetDatagramMessageIdArrayLength": "8192",
    "DFIntTargetTimeDelayFacctorTenths": "1",
    "DFIntInterpolationDtLimitForLod": "-10",
    "FIntTaskSchedulerMaxNumOfJobs": "**********",
    "DFIntPerformanceControlFrameTimeMax": "1",
    "FIntRuntimeMaxNumOfLatches": "20000",
    "DFIntVoiceChatRollOffMode": "2",
    "DFIntActionStationDebounceTime": "0",
    "DFIntRuntimeConcurrency": "**********",
    "DFIntMaxReceiveToDeserializeLatencyMilliseconds": "1",
    "FIntRuntimeMaxNumOfConditions": "20000",
    "FIntNumFramesToCaptureCallStack": "-1000",
    "DFIntVoiceChatRollOffMinDistance": "1",
    "DFIntAccelerationTimeThreshold": "0",
    "FIntRuntimeMaxNumOfMutexes": "20000",
    "DFIntRakNetPingFrequencyMillisecond": "10",
    "DFIntRakNetApplicationFeedbackMaxSpeedBPS": "**********",
    "DFIntRakNetApplicationFeedbackInitialSpeedBPS": "**********",
    "FIntRuntimeMaxNumOfThreads": "20000",
    "DFIntGameNetPVHeaderRotationalVelocityZeroCutoffExponent": "-1",
    "DFIntGameNetPVHeaderRotationOrientIdToleranceExponent": "-1",
    "DFIntGameNetPVHeaderTranslationZeroCutoffExponent": "-1",
    "DFIntGameNetPVHeaderLinearVelocityZeroCutoffExponent": "-1",
    "DFIntMinimumNumberMechanismsForMT": "1",
    "FIntRuntimeMaxNumOfSchedulers": "20000",
    "DFIntClientPacketMaxFrameMicroseconds": "1",
    "DFIntClientPacketHealthyMsPerSecondLimit": "1",
    "DFIntClientPacketUnhealthyContEscMsPerSecond": "1",
    "DFIntClientPacketMinMicroseconds": "1",
    "DFIntClientPacketExcessMicroseconds": "1",
    "DFFlagTextureQualityOverrideEnabled": "True",
    "DFIntTextureQualityOverride": "0",
    "DFIntInterpolationMinAssemblyCount": "1"
  };

  async function checkBloxstrapInstallation() {
    try {
      const fs = require('fs');
      const path = require('path');
      const os = require('os');

      const possiblePaths = [
        path.join(os.homedir(), 'AppData', 'Local', 'Bloxstrap'),
        path.join(os.homedir(), 'AppData', 'Roaming', 'Bloxstrap'),
        'C:\\Program Files\\Bloxstrap',
        'C:\\Program Files (x86)\\Bloxstrap'
      ];

      for (const bloxstrapPath of possiblePaths) {
        if (fs.existsSync(bloxstrapPath)) {
          const clientSettingsDir = path.join(bloxstrapPath, 'Modifications', 'ClientSettings');
          const configPath = path.join(clientSettingsDir, 'ClientAppSettings.json');

          if (!fs.existsSync(clientSettingsDir)) {
            fs.mkdirSync(clientSettingsDir, { recursive: true });
          }

          return { installed: true, path: bloxstrapPath, configPath };
        }
      }

      return { installed: false };
    } catch (error) {
      console.error('Error checking Bloxstrap installation:', error);
      return { installed: false };
    }
  }

  function updateBloxstrapStatus(isInstalled) {
    const statusDot = bloxstrapStatus.querySelector('.status-dot');
    const statusText = bloxstrapStatus.querySelector('.status-text');

    statusDot.classList.remove('detected', 'not-detected', 'inactive');

    if (isInstalled) {
      statusDot.classList.add('detected');
      statusText.textContent = 'Detected';
      addFFlagsButton.disabled = false;
      removeFFlagsButton.disabled = false;
    } else {
      statusDot.classList.add('not-detected');
      statusText.textContent = 'Not Detected';
      addFFlagsButton.disabled = true;
      removeFFlagsButton.disabled = true;
    }
  }

  async function readBloxstrapFFlags(configPath) {
    try {
      const fs = require('fs');

      if (!fs.existsSync(configPath)) {
        return {};
      }

      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);

      return config || {};
    } catch (error) {
      console.error('Error reading Bloxstrap FFlags:', error);
      return {};
    }
  }

  async function writeBloxstrapFFlags(configPath, newFFlags) {
    try {
      const fs = require('fs');

      let config = {};
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        config = JSON.parse(configData);
      }

      Object.entries(newFFlags).forEach(([key, value]) => {
        config[key] = value;
      });

      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      return true;
    } catch (error) {
      console.error('Error writing Bloxstrap FFlags:', error);
      return false;
    }
  }

  async function removeBloxstrapFFlags(configPath, fflagsToRemove) {
    try {
      const fs = require('fs');

      if (!fs.existsSync(configPath)) {
        return true;
      }

      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);

      Object.keys(fflagsToRemove).forEach(key => {
        delete config[key];
      });

      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      return true;
    } catch (error) {
      console.error('Error removing Bloxstrap FFlags:', error);
      return false;
    }
  }

  function updateFFlagStatus(currentFFlags) {
    const ballLagActive = currentFFlags['DFFlagDebugPerfMode'] === 'True' &&
                         currentFFlags['FFlagOptimizeNetwork'] === 'True' &&
                         currentFFlags['DFIntTaskSchedulerTargetFps'] === '9999';

    fflagBallLag.classList.remove('not-set', 'enabled', 'disabled');
    if (Object.keys(currentFFlags).length > 0 && ballLagActive) {
      fflagBallLag.classList.add('enabled');
      fflagBallLag.textContent = 'Active';
    } else if (Object.keys(currentFFlags).length > 0) {
      fflagBallLag.classList.add('disabled');
      fflagBallLag.textContent = 'Partial';
    } else {
      fflagBallLag.classList.add('not-set');
      fflagBallLag.textContent = 'Not Set';
    }

    // Update individual FFlag displays for key Ball Lag FFlags
    const fflags = [
      { element: fflagMouseIcon, key: 'DFFlagDebugPerfMode' },
      { element: fflagCollectionService, key: 'FFlagOptimizeNetwork' },
      { element: fflagInputChanged, key: 'DFIntTaskSchedulerTargetFps' }
    ];

    fflags.forEach(({ element, key }) => {
      element.classList.remove('not-set', 'enabled', 'disabled');

      if (currentFFlags.hasOwnProperty(key)) {
        if (currentFFlags[key]) {
          element.classList.add('enabled');
          element.textContent = 'Enabled';
        } else {
          element.classList.add('disabled');
          element.textContent = 'Disabled';
        }
      } else {
        element.classList.add('not-set');
        element.textContent = 'Not Set';
      }
    });
  }

  // Keybind functionality
  const toggleClickKeybind = document.getElementById('toggle-click-keybind');
  const toggleButterflyKeybind = document.getElementById('toggle-butterfly-keybind');
  const increaseDelayKeybind = document.getElementById('increase-delay-keybind');
  const decreaseDelayKeybind = document.getElementById('decrease-delay-keybind');

  // Keybind storage
  let keybinds = {
    toggleClick: 'F6',
    toggleButterfly: 'F7',
    increaseDelay: 'F8',
    decreaseDelay: 'F9',
    scrollUp: 'ArrowUp',
    scrollDown: 'ArrowDown',
    scrollPageUp: 'PageUp',
    scrollPageDown: 'PageDown'
  };

  // Load saved keybinds
  function loadKeybinds() {
    const savedKeybinds = localStorage.getItem('keybinds');
    if (savedKeybinds) {
      keybinds = JSON.parse(savedKeybinds);
      toggleClickKeybind.textContent = keybinds.toggleClick;
      toggleButterflyKeybind.textContent = keybinds.toggleButterfly;
      increaseDelayKeybind.textContent = keybinds.increaseDelay;
      decreaseDelayKeybind.textContent = keybinds.decreaseDelay;
    }
  }

  // Save keybinds
  function saveKeybinds() {
    localStorage.setItem('keybinds', JSON.stringify(keybinds));
  }

  // Handle keybind button clicks
  function setupKeybindListeners() {
    const keybindButtons = [
      { element: toggleClickKeybind, key: 'toggleClick' },
      { element: toggleButterflyKeybind, key: 'toggleButterfly' },
      { element: increaseDelayKeybind, key: 'increaseDelay' },
      { element: decreaseDelayKeybind, key: 'decreaseDelay' }
    ];

    keybindButtons.forEach(({ element, key }) => {
      element.addEventListener('click', () => {
        // Remove listening class from all buttons
        keybindButtons.forEach(kb => kb.element.classList.remove('listening'));

        // Add listening class to clicked button
        element.classList.add('listening');
        element.textContent = 'Press a key...';

        // Function to handle keydown
        const handleKeyDown = (e) => {
          e.preventDefault();

          // Get key name
          let keyName = e.key;
          if (e.key === ' ') keyName = 'Space';
          if (e.key.length === 1) keyName = e.key.toUpperCase();

          // Update keybind
          keybinds[key] = keyName;
          element.textContent = keyName;
          element.classList.remove('listening');

          // Save keybinds
          saveKeybinds();

          // Remove event listener
          window.removeEventListener('keydown', handleKeyDown);
        };

        // Add keydown event listener
        window.addEventListener('keydown', handleKeyDown, { once: true });
      });
    });
  }

  // Handle global keybinds
  function setupGlobalKeybinds() {
    window.addEventListener('keydown', (e) => {
      // Don't trigger if in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
        return;
      }

      // Check keybinds
      if (e.key === keybinds.toggleClick) {
        e.preventDefault();
        autoClickToggle.click();
      } else if (e.key === keybinds.toggleButterfly) {
        e.preventDefault();
        butterflyModeToggle.click();
      } else if (e.key === keybinds.increaseDelay) {
        e.preventDefault();
        // Increase delay by 5ms
        let currentDelay = parseInt(clickDelayValue.textContent);
        if (currentDelay < 100) {
          currentDelay = Math.min(100, currentDelay + 5);
          updateClickDelayUI(currentDelay);
          if (window.cppAddon) {
            try {
              window.cppAddon.setClickDelay(currentDelay);
            } catch (error) {
              console.error('Error setting click delay:', error);
            }
          }
        }
      } else if (e.key === keybinds.decreaseDelay) {
        e.preventDefault();
        // Decrease delay by 5ms
        let currentDelay = parseInt(clickDelayValue.textContent);
        if (currentDelay > 1) {
          currentDelay = Math.max(1, currentDelay - 5);
          updateClickDelayUI(currentDelay);
          if (window.cppAddon) {
            try {
              window.cppAddon.setClickDelay(currentDelay);
            } catch (error) {
              console.error('Error setting click delay:', error);
            }
          }
        }
      } else if (e.key === keybinds.scrollUp) {
        e.preventDefault();
        // Smooth scroll up
        const mainContent = document.querySelector('.main-content');
        mainContent.scrollBy({ top: -50, behavior: 'smooth' });
      } else if (e.key === keybinds.scrollDown) {
        e.preventDefault();
        // Smooth scroll down
        const mainContent = document.querySelector('.main-content');
        mainContent.scrollBy({ top: 50, behavior: 'smooth' });
      } else if (e.key === keybinds.scrollPageUp) {
        e.preventDefault();
        // Scroll page up
        const mainContent = document.querySelector('.main-content');
        mainContent.scrollBy({ top: -mainContent.clientHeight * 0.9, behavior: 'smooth' });
      } else if (e.key === keybinds.scrollPageDown) {
        e.preventDefault();
        // Scroll page down
        const mainContent = document.querySelector('.main-content');
        mainContent.scrollBy({ top: mainContent.clientHeight * 0.9, behavior: 'smooth' });
      }
    });
  }

  // Add FFlags button handler
  addFFlagsButton.addEventListener('click', async () => {
    addFFlagsButton.classList.add('loading');
    addFFlagsButton.textContent = '';

    try {
      const bloxstrapInfo = await checkBloxstrapInstallation();
      if (!bloxstrapInfo.installed) {
        throw new Error('Bloxstrap not detected');
      }

      const success = await writeBloxstrapFFlags(bloxstrapInfo.configPath, autoClickerFFlags);

      if (success) {
        addFFlagsButton.classList.remove('loading');
        addFFlagsButton.classList.add('fflag-success');
        addFFlagsButton.textContent = 'Added Successfully!';

        // Update status display
        const currentFFlags = await readBloxstrapFFlags(bloxstrapInfo.configPath);
        updateFFlagStatus(currentFFlags);

        setTimeout(() => {
          addFFlagsButton.classList.remove('fflag-success');
          addFFlagsButton.textContent = 'Add Ball Lag FFlags';
        }, 3000);
      } else {
        throw new Error('Failed to write FFlags');
      }
    } catch (error) {
      addFFlagsButton.classList.remove('loading');
      addFFlagsButton.classList.add('fflag-error');
      addFFlagsButton.textContent = 'Error!';
      console.error('Error adding FFlags:', error);

      setTimeout(() => {
        addFFlagsButton.classList.remove('fflag-error');
        addFFlagsButton.textContent = 'Add Ball Lag FFlags';
      }, 3000);
    }
  });

  // Remove FFlags button handler
  removeFFlagsButton.addEventListener('click', async () => {
    removeFFlagsButton.classList.add('loading');
    removeFFlagsButton.textContent = '';

    try {
      const bloxstrapInfo = await checkBloxstrapInstallation();
      if (!bloxstrapInfo.installed) {
        throw new Error('Bloxstrap not detected');
      }

      // Use the dedicated remove function
      const success = await removeBloxstrapFFlags(bloxstrapInfo.configPath, autoClickerFFlags);

      if (success) {
        removeFFlagsButton.classList.remove('loading');
        removeFFlagsButton.classList.add('fflag-success');
        removeFFlagsButton.textContent = 'Removed Successfully!';

        // Update status display
        const currentFFlags = await readBloxstrapFFlags(bloxstrapInfo.configPath);
        updateFFlagStatus(currentFFlags);

        setTimeout(() => {
          removeFFlagsButton.classList.remove('fflag-success');
          removeFFlagsButton.textContent = 'Remove Ball Lag FFlags';
        }, 3000);
      } else {
        throw new Error('Failed to remove FFlags');
      }
    } catch (error) {
      removeFFlagsButton.classList.remove('loading');
      removeFFlagsButton.classList.add('fflag-error');
      removeFFlagsButton.textContent = 'Error!';
      console.error('Error removing FFlags:', error);

      setTimeout(() => {
        removeFFlagsButton.classList.remove('fflag-error');
        removeFFlagsButton.textContent = 'Remove Ball Lag FFlags';
      }, 3000);
    }
  });

  // Initialize Bloxstrap integration
  async function initializeBloxstrapIntegration() {
    const bloxstrapInfo = await checkBloxstrapInstallation();
    updateBloxstrapStatus(bloxstrapInfo.installed);

    if (bloxstrapInfo.installed) {
      const currentFFlags = await readBloxstrapFFlags(bloxstrapInfo.configPath);
      updateFFlagStatus(currentFFlags);
    }
  }

  // Initialize keybinds
  loadKeybinds();
  setupKeybindListeners();
  setupGlobalKeybinds();

  // Initialize Bloxstrap integration
  initializeBloxstrapIntegration();

  // Enhanced page transitions with animations
  function switchToPage(targetPage, targetMenu) {
    // Remove active from all pages and menus
    document.querySelectorAll('.page.active').forEach(page => {
      page.classList.add('slide-out');
      setTimeout(() => {
        page.classList.remove('active', 'slide-out');
      }, 200);
    });

    document.querySelectorAll('.menu-item.active').forEach(menu => {
      menu.classList.remove('active');
    });

    // Add active to target elements with delay for smooth transition
    setTimeout(() => {
      targetPage.classList.add('active');
      targetMenu.classList.add('active');
    }, 200);
  }

  menuMain.addEventListener('click', () => {
    if (!menuMain.classList.contains('active')) {
      switchToPage(mainPage, menuMain);
    }
  });

  menuSettings.addEventListener('click', () => {
    if (!menuSettings.classList.contains('active')) {
      switchToPage(settingsPage, menuSettings);
    }
  });

  // References to UI elements
  const autoClickToggle = document.getElementById('auto-click-toggle');
  const butterflyModeToggle = document.getElementById('butterfly-mode-toggle');
  const clickDelaySlider = document.getElementById('click-delay-slider');
  const clickDelayValue = document.getElementById('click-delay-value');
  const clickCounter = document.getElementById('click-counter');
  const clickRate = document.getElementById('click-rate');
  const resetCounter = document.getElementById('reset-counter');
  const robloxStatus = document.getElementById('roblox-status');
  const clickStatus = document.getElementById('click-status');

  let lastClickCount = 0;
  let clickHistory = [];
  let lastUpdateTime = Date.now();
  const butterflyKey1 = document.getElementById('butterfly-key1');
  const butterflyKey2 = document.getElementById('butterfly-key2');
  const clickKey = document.getElementById('click-key');
  const bindKey1 = document.getElementById('bind-key1');
  const bindKey2 = document.getElementById('bind-key2');
  const bindClickKey = document.getElementById('bind-click-key');
  const startButton = document.getElementById('start-button');
  const stopButton = document.getElementById('stop-button');

  // Binding state
  let isBinding = false;
  let bindingTarget = null;

  // Initialize UI based on C++ addon state
  function initializeUI() {
    if (window.cppAddon) {
      try {
        // Auto click toggle
        const autoClickState = window.cppAddon.getAutoClickState();
        autoClickToggle.classList.toggle('enabled', autoClickState);

        // Butterfly mode toggle
        const butterflyMode = window.cppAddon.getButterflyMode();
        butterflyModeToggle.classList.toggle('enabled', butterflyMode);

        // Click delay
        const clickDelay = window.cppAddon.getClickDelay();
        updateClickDelayUI(clickDelay);

        // Butterfly keys
        const butterflyKeys = window.cppAddon.getButterflyKeys();
        butterflyKey1.textContent = butterflyKeys.key1Name;
        butterflyKey2.textContent = butterflyKeys.key2Name;

        // Click key
        const clickKeyInfo = window.cppAddon.getClickKey();
        clickKey.textContent = clickKeyInfo.keyName;

        // Start the clicker
        window.cppAddon.startClicker();
      } catch (error) {
        console.error('Error initializing UI:', error);
      }
    }
  }

  // Enhanced UI update with animations and click rate
  function updateUI() {
    if (window.cppAddon) {
      try {
        // Update click counter with animation
        const count = window.cppAddon.getClickCounter();
        if (count !== lastClickCount) {
          // Animate counter change
          clickCounter.classList.add('updated');
          setTimeout(() => clickCounter.classList.remove('updated'), 500);

          // Update click history for rate calculation
          const now = Date.now();
          clickHistory.push({ count: count - lastClickCount, time: now });

          // Keep only last 5 seconds of history
          clickHistory = clickHistory.filter(entry => now - entry.time <= 5000);

          // Calculate clicks per second
          const totalClicks = clickHistory.reduce((sum, entry) => sum + entry.count, 0);
          const timeSpan = clickHistory.length > 0 ? (now - clickHistory[0].time) / 1000 : 1;
          const cps = Math.round((totalClicks / Math.max(timeSpan, 1)) * 10) / 10;

          // Update displays
          clickCounter.textContent = count;
          clickRate.textContent = `${cps} CPS`;

          // Animate click rate if actively clicking
          if (cps > 0) {
            clickRate.classList.add('active');
            setTimeout(() => clickRate.classList.remove('active'), 1000);
          }

          lastClickCount = count;
        }

        // Update Roblox status with enhanced animations
        const isRobloxActive = window.cppAddon.isRobloxActive();
        const robloxDot = robloxStatus.querySelector('.status-dot');
        const robloxText = robloxStatus.querySelector('.status-text');

        // Remove all status classes first
        robloxDot.classList.remove('active', 'inactive', 'warning');

        if (isRobloxActive) {
          robloxDot.classList.add('active');
          robloxText.textContent = 'Active';
        } else {
          robloxDot.classList.add('inactive');
          robloxText.textContent = 'Inactive';
        }

        // Update click status with enhanced feedback
        const isClicking = window.cppAddon.isClicking();
        const clickDot = clickStatus.querySelector('.status-dot');
        const clickText = clickStatus.querySelector('.status-text');

        // Remove all status classes first
        clickDot.classList.remove('active', 'inactive', 'warning');

        if (isClicking) {
          clickDot.classList.add('active');
          clickText.textContent = 'Clicking';
        } else if (window.cppAddon.getAutoClickState() && isRobloxActive) {
          clickDot.classList.add('warning');
          clickText.textContent = 'Ready';
        } else {
          clickDot.classList.add('inactive');
          clickText.textContent = 'Not Clicking';
        }
      } catch (error) {
        console.error('Error updating UI:', error);
      }
    }

    // Update UI every 100ms
    setTimeout(updateUI, 100);
  }

  // Update click delay UI
  function updateClickDelayUI(delay) {
    // Calculate percentage (max delay is 100ms)
    const percentage = Math.min((delay / 100) * 100, 100);
    clickDelaySlider.querySelector('.slider-fill').style.width = `${percentage}%`;
    clickDelayValue.textContent = `${delay}ms`;
  }

  // Enhanced toggle functionality with animations
  function addRippleEffect(element) {
    element.classList.add('ripple');
    setTimeout(() => element.classList.remove('ripple'), 600);
  }

  function setToggleLoading(toggle, isLoading) {
    if (isLoading) {
      toggle.classList.add('loading');
    } else {
      toggle.classList.remove('loading');
    }
  }

  // Handle auto click toggle with enhanced feedback
  autoClickToggle.addEventListener('click', () => {
    addRippleEffect(autoClickToggle);
    setToggleLoading(autoClickToggle, true);

    setTimeout(() => {
      autoClickToggle.classList.toggle('enabled');
      const isEnabled = autoClickToggle.classList.contains('enabled');

      if (window.cppAddon) {
        try {
          window.cppAddon.toggleAutoClick(isEnabled);
        } catch (error) {
          console.error('Error toggling auto click:', error);
        }
      }

      setToggleLoading(autoClickToggle, false);
    }, 200);
  });

  // Handle butterfly mode toggle with enhanced feedback
  butterflyModeToggle.addEventListener('click', () => {
    addRippleEffect(butterflyModeToggle);
    setToggleLoading(butterflyModeToggle, true);

    setTimeout(() => {
      butterflyModeToggle.classList.toggle('enabled');
      const isEnabled = butterflyModeToggle.classList.contains('enabled');

      if (window.cppAddon) {
        try {
          window.cppAddon.toggleButterflyMode(isEnabled);
        } catch (error) {
          console.error('Error toggling butterfly mode:', error);
        }
      }

      setToggleLoading(butterflyModeToggle, false);
    }, 200);
  });

  // Handle click delay slider with both click and drag support
  let isDragging = false;

  // Function to update the slider value based on mouse position
  function updateSliderValue(event) {
    const rect = clickDelaySlider.getBoundingClientRect();
    const x = Math.min(Math.max(event.clientX - rect.left, 0), rect.width);
    const percentage = (x / rect.width) * 100;

    // Calculate delay (1-100ms range)
    const delay = Math.max(1, Math.round(percentage));
    updateClickDelayUI(delay);

    if (window.cppAddon) {
      try {
        window.cppAddon.setClickDelay(delay);
      } catch (error) {
        console.error('Error setting click delay:', error);
      }
    }
  }

  // Handle click (for single click positioning)
  clickDelaySlider.addEventListener('click', (event) => {
    if (!isDragging) { // Only handle click if we're not ending a drag operation
      updateSliderValue(event);
    }
  });

  // Handle drag operations
  clickDelaySlider.addEventListener('mousedown', (event) => {
    isDragging = true;
    updateSliderValue(event);

    // Add a class to indicate active dragging
    clickDelaySlider.classList.add('dragging');

    // Prevent text selection during drag
    event.preventDefault();
  });

  // Track mouse movement while dragging
  document.addEventListener('mousemove', (event) => {
    if (isDragging) {
      updateSliderValue(event);
    }
  });

  // End drag operation when mouse is released
  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      clickDelaySlider.classList.remove('dragging');
    }
  });

  // End drag if mouse leaves the window
  document.addEventListener('mouseleave', () => {
    if (isDragging) {
      isDragging = false;
      clickDelaySlider.classList.remove('dragging');
    }
  });

  // Handle reset counter button with enhanced feedback
  resetCounter.addEventListener('click', () => {
    resetCounter.classList.add('loading');
    resetCounter.textContent = '';

    setTimeout(() => {
      if (window.cppAddon) {
        try {
          window.cppAddon.resetClickCounter();
          clickCounter.textContent = '0';
          clickRate.textContent = '0 CPS';
          lastClickCount = 0;
          clickHistory = [];

          // Animate the reset
          clickCounter.classList.add('updated');
          setTimeout(() => clickCounter.classList.remove('updated'), 500);
        } catch (error) {
          console.error('Error resetting counter:', error);
        }
      }

      resetCounter.classList.remove('loading');
      resetCounter.textContent = 'Reset';
    }, 500);
  });

  // Handle key binding
  function startBinding(target) {
    isBinding = true;
    bindingTarget = target;
    target.textContent = 'Press a key...';
  }

  function stopBinding() {
    isBinding = false;
    bindingTarget = null;
  }

  // Global key press handler for binding
  document.addEventListener('keydown', (event) => {
    if (isBinding && bindingTarget) {
      event.preventDefault();
      const keyCode = event.keyCode || event.which;
      const keyChar = String.fromCharCode(keyCode);

      if (window.cppAddon) {
        try {
          if (bindingTarget === butterflyKey1) {
            const keys = window.cppAddon.getButterflyKeys();
            window.cppAddon.setButterflyKeys(keyCode, keys.key2);
            butterflyKey1.textContent = keyChar;
          } else if (bindingTarget === butterflyKey2) {
            const keys = window.cppAddon.getButterflyKeys();
            window.cppAddon.setButterflyKeys(keys.key1, keyCode);
            butterflyKey2.textContent = keyChar;
          } else if (bindingTarget === clickKey) {
            window.cppAddon.setClickKey(keyCode);
            clickKey.textContent = keyChar;
          }
        } catch (error) {
          console.error('Error setting key binding:', error);
        }
      }

      stopBinding();
    }
  });

  // Bind key buttons
  bindKey1.addEventListener('click', () => startBinding(butterflyKey1));
  bindKey2.addEventListener('click', () => startBinding(butterflyKey2));
  bindClickKey.addEventListener('click', () => startBinding(clickKey));

  // Enhanced start button with loading state
  startButton.addEventListener('click', () => {
    startButton.classList.add('loading');
    startButton.textContent = '';

    setTimeout(() => {
      if (window.cppAddon) {
        try {
          window.cppAddon.startClicker();
          // Disable start button after clicking to prevent multiple clicks
          startButton.disabled = true;
          startButton.classList.add('disabled');
          startButton.classList.remove('loading');
          startButton.textContent = 'Start';
          // Enable stop button
          stopButton.disabled = false;
          stopButton.classList.remove('disabled');
        } catch (error) {
          console.error('Error starting clicker:', error);
          startButton.classList.remove('loading');
          startButton.textContent = 'Start';
        }
      }
    }, 300);
  });

  // Enhanced stop button with loading state
  stopButton.addEventListener('click', () => {
    stopButton.classList.add('loading');
    stopButton.textContent = '';

    setTimeout(() => {
      if (window.cppAddon) {
        try {
          window.cppAddon.stopClicker();
          // Re-enable start button after stopping
          startButton.disabled = false;
          startButton.classList.remove('disabled');
          // Disable stop button
          stopButton.disabled = true;
          stopButton.classList.add('disabled');
          stopButton.classList.remove('loading');
          stopButton.textContent = 'Stop';
        } catch (error) {
          console.error('Error stopping clicker:', error);
          stopButton.classList.remove('loading');
          stopButton.textContent = 'Stop';
        }
      }
    }, 300);
  });

  // Initialize UI and start update loop
  initializeUI();
  updateUI();

  // Initialize stop button to be disabled by default
  stopButton.disabled = true;
  stopButton.classList.add('disabled');

  // Implement direct functionality for the auto-clicker if C++ addon is not available
  if (!window.cppAddon) {
    console.log('Implementing direct auto-clicker functionality');

    // State variables
    const state = {
      isRunning: false,
      autoClickEnabled: false,
      butterflyMode: true,
      clickDelay: 5,
      clickCounter: 0,
      butterflyKey1: { code: 70, name: 'F' }, // F key
      butterflyKey2: { code: 71, name: 'G' }, // G key
      clickKey: { code: 2, name: 'Right Mouse' }, // Right mouse button
      isRobloxActive: false,
      isClicking: false,
      clickInterval: null,
      statusInterval: null
    };

    // Check if Roblox is active (simplified version)
    function checkRobloxActive() {
      // In a real implementation, this would check the active window
      // For now, we'll just return true for testing
      return true;
    }

    // Start the auto-clicker
    function startClicker() {
      if (state.isRunning) return;

      state.isRunning = true;
      console.log('Auto-clicker started');

      // Start status check interval
      state.statusInterval = setInterval(() => {
        state.isRobloxActive = checkRobloxActive();

        // Check for key presses if auto-click is enabled and Roblox is active
        if (state.autoClickEnabled && state.isRobloxActive) {
          // In a real implementation, this would check actual key states
          // For now, we'll just simulate random clicking for UI feedback
          state.isClicking = Math.random() > 0.7;

          if (state.isClicking) {
            state.clickCounter++;
          }
        } else {
          state.isClicking = false;
        }
      }, 100);
    }

    // Stop the auto-clicker
    function stopClicker() {
      if (!state.isRunning) return;

      state.isRunning = false;
      state.isClicking = false;

      if (state.statusInterval) {
        clearInterval(state.statusInterval);
        state.statusInterval = null;
      }

      console.log('Auto-clicker stopped');
    }

    // Create the mock C++ addon interface
    window.cppAddon = {
      startClicker: () => startClicker(),
      stopClicker: () => stopClicker(),
      setClickDelay: (delay) => {
        state.clickDelay = delay;
        console.log(`Click delay set to ${delay}ms`);
      },
      getClickDelay: () => state.clickDelay,
      toggleAutoClick: (enabled) => {
        state.autoClickEnabled = enabled;
        console.log(`Auto click ${enabled ? 'enabled' : 'disabled'}`);
      },
      getAutoClickState: () => state.autoClickEnabled,
      toggleButterflyMode: (enabled) => {
        state.butterflyMode = enabled;
        console.log(`Butterfly mode ${enabled ? 'enabled' : 'disabled'}`);
      },
      getButterflyMode: () => state.butterflyMode,
      setButterflyKeys: (key1, key2) => {
        state.butterflyKey1.code = key1;
        state.butterflyKey1.name = String.fromCharCode(key1);
        state.butterflyKey2.code = key2;
        state.butterflyKey2.name = String.fromCharCode(key2);
        console.log(`Butterfly keys set to ${state.butterflyKey1.name} and ${state.butterflyKey2.name}`);
      },
      getButterflyKeys: () => ({
        key1: state.butterflyKey1.code,
        key2: state.butterflyKey2.code,
        key1Name: state.butterflyKey1.name,
        key2Name: state.butterflyKey2.name
      }),
      setClickKey: (key) => {
        state.clickKey.code = key;
        state.clickKey.name = String.fromCharCode(key);
        console.log(`Click key set to ${state.clickKey.name}`);
      },
      getClickKey: () => ({
        key: state.clickKey.code,
        keyName: state.clickKey.name
      }),
      getClickCounter: () => state.clickCounter,
      resetClickCounter: () => {
        state.clickCounter = 0;
        console.log('Click counter reset');
      },
      isRobloxActive: () => state.isRobloxActive,
      isClicking: () => state.isClicking
    };

    // Add global keyboard event listeners for actual clicking
    document.addEventListener('keydown', (event) => {
      if (!state.isRunning || !state.autoClickEnabled || !state.isRobloxActive) return;

      const keyCode = event.keyCode || event.which;

      if (state.butterflyMode) {
        if (keyCode === state.butterflyKey1.code || keyCode === state.butterflyKey2.code) {
          // Simulate clicking
          state.isClicking = true;
          state.clickCounter += 2; // Double click for butterfly mode

          // Reset clicking state after a short delay
          setTimeout(() => {
            state.isClicking = false;
          }, 100);
        }
      } else {
        // Legacy mode - check for click key
        if (keyCode === state.clickKey.code) {
          state.isClicking = true;
        }
      }
    });

    document.addEventListener('keyup', (event) => {
      if (!state.isRunning || !state.autoClickEnabled || !state.isRobloxActive) return;

      const keyCode = event.keyCode || event.which;

      if (!state.butterflyMode && keyCode === state.clickKey.code) {
        state.isClicking = false;
      }
    });

    document.addEventListener('keydown', (event) => {
      if (event.keyCode === 45) {
        const newState = !state.autoClickEnabled;
        state.autoClickEnabled = newState;
        autoClickToggle.classList.toggle('enabled', newState);
        console.log(`Auto click ${newState ? 'enabled' : 'disabled'} by INSERT key`);
      }
    });

    initializeUI();
    startClicker(); // Auto-start the clicker
  }
});
