// This script runs after the package is created but before it's finalized
// It can be used to modify the package contents if needed

const fs = require('fs');
const path = require('path');

module.exports = async function(context) {
  console.log('Post-package hook running...');
  
  // You can access context.appOutDir to get the directory where the app is being built
  const appOutDir = context.appOutDir;
  console.log('App output directory:', appOutDir);
  
  // Example: Ensure the build directory is properly copied
  const buildDir = path.join(appOutDir, 'resources', 'build');
  
  try {
    // Log if the build directory exists in the output
    if (fs.existsSync(buildDir)) {
      console.log('Build directory exists in output:', buildDir);
      
      // List files in the build directory
      const files = fs.readdirSync(buildDir);
      console.log('Files in build directory:', files);
    } else {
      console.warn('Build directory not found in output:', buildDir);
    }
  } catch (error) {
    console.error('Error in post-package hook:', error);
  }
  
  console.log('Post-package hook completed');
};
