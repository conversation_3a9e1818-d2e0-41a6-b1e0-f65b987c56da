/* Theme CSS Variables */
:root {
  /* Default Dark Theme */
  --bg-primary: #0d0d12;
  --bg-secondary: #101016;
  --bg-tertiary: #1a1a24;
  --bg-sidebar: rgba(10, 10, 14, 0.3);
  --border-color: rgba(26, 26, 36, 0.2);
  --text-primary: #e6e6e6;
  --text-secondary: #8a8a9a;
  --accent-color: #6c5ce7;
  --accent-glow: rgba(108, 92, 231, 0.6);
  --accent-hover: #8a7eeb;
  --danger-color: #e81123;
  --success-color: #10b981;
  --warning-color: #f59e0b;

  /* Scale factor to make content appear less zoomed in */
  --ui-scale: 0.95;
}

/* Neon Purple Theme */
[data-theme="neon-purple"] {
  --bg-primary: #0d0d12;
  --bg-secondary: #101016;
  --bg-tertiary: #1a1a24;
  --bg-sidebar: rgba(10, 10, 14, 0.3);
  --border-color: rgba(26, 26, 36, 0.2);
  --text-primary: #e6e6e6;
  --text-secondary: #8a8a9a;
  --accent-color: #9333ea;
  --accent-glow: rgba(147, 51, 234, 0.6);
  --accent-hover: #a855f7;
  --danger-color: #e81123;
  --success-color: #10b981;
  --warning-color: #f59e0b;
}

/* Ocean Blue Theme */
[data-theme="ocean-blue"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-sidebar: rgba(15, 23, 42, 0.3);
  --border-color: rgba(51, 65, 85, 0.2);
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --accent-color: #3b82f6;
  --accent-glow: rgba(59, 130, 246, 0.6);
  --accent-hover: #60a5fa;
  --danger-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
}

/* Emerald Green Theme */
[data-theme="emerald-green"] {
  --bg-primary: #0c1f17;
  --bg-secondary: #0f291e;
  --bg-tertiary: #134e37;
  --bg-sidebar: rgba(12, 31, 23, 0.3);
  --border-color: rgba(19, 78, 55, 0.2);
  --text-primary: #ecfdf5;
  --text-secondary: #a7f3d0;
  --accent-color: #10b981;
  --accent-glow: rgba(16, 185, 129, 0.6);
  --accent-hover: #34d399;
  --danger-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
}

/* Amber Gold Theme */
[data-theme="amber-gold"] {
  --bg-primary: #1c1917;
  --bg-secondary: #292524;
  --bg-tertiary: #44403c;
  --bg-sidebar: rgba(28, 25, 23, 0.3);
  --border-color: rgba(68, 64, 60, 0.2);
  --text-primary: #fef3c7;
  --text-secondary: #d9d9d9;
  --accent-color: #f59e0b;
  --accent-glow: rgba(245, 158, 11, 0.6);
  --accent-hover: #fbbf24;
  --danger-color: #ef4444;
  --success-color: #10b981;
  --warning-color: #f59e0b;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  user-select: none;
  -webkit-user-drag: none;
  -webkit-app-region: no-drag;
  touch-action: none; /* Disable touch zooming */
}

body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-size: calc(1em * var(--ui-scale));
}

/* Fullscreen mode adjustments */
:fullscreen body, body:fullscreen {
  width: 100%;
  height: 100vh;
}

.click-counter-container {
  display: flex;
  align-items: center;
  gap: 100px;
  margin-top: 30px;
  margin-bottom: 10px;
}

/* Custom title bar styles */
.title-bar {
  height: 30px;
  background-color: var(--bg-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  -webkit-app-region: drag; /* Makes the title bar draggable */
  user-select: none;
  padding: 0 10px;
  border-bottom: 1px solid var(--border-color);
}

.title-bar-text {
  color: var(--text-primary);
  font-size: 12px;
  margin-left: 5px;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag; /* Makes the buttons clickable */
}

.window-control-button {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
}

.window-control-button:hover {
  background-color: var(--bg-tertiary);
}

.window-control-close:hover {
  background-color: var(--danger-color);
}

#fullscreen-button:hover {
  background-color: var(--accent-color);
  color: white;
}

/* Menu Bar Styles */
.menu-bar {
  display: flex;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  height: 40px;
  -webkit-app-region: no-drag;
}

.menu-item {
  padding: 0 20px;
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.menu-item:hover {
  color: var(--accent-hover);
}

.menu-item.active {
  color: var(--accent-color);
}

.menu-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--accent-color);
  box-shadow: 0 0 8px var(--accent-glow);
}

.container {
  display: flex;
  flex: 1;
  height: calc(100vh - 70px); /* Adjusted for title bar (30px) + menu bar (40px) */
}

.sidebar {
  width: 120px;
  background-color: var(--bg-sidebar);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 60px 0;
  border-right: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

/* Add a subtle gradient to the sidebar with ancient pattern */
.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(to bottom, var(--bg-sidebar), rgba(0, 0, 0, 0.4), var(--bg-sidebar)),
    url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%236c5ce7' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 20L20 0L10 0L0 10M40 20L20 40L30 40L40 30'/%3E%3C/g%3E%3C/svg%3E");
  z-index: -1;
  opacity: 0.8;
}

/* Vertical text styling */
.vertical-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.vertical-text .letter {
  color: var(--accent-color);
  font-size: 18px;
  font-weight: 600;
  margin: 4px 0;
  opacity: 0.9;
  transition: opacity 0.2s ease, transform 0.2s ease;
  animation: pulse-text 3s infinite alternate;
  font-family: 'Ma Shan Zheng', 'ZCOOL QingKe HuangYou', cursive; /* Spectacular Chinese font */
  text-shadow: 0 0 8px var(--accent-glow);
  letter-spacing: 1px;
}

.vertical-text .letter.space {
  height: 6px;
}

.vertical-text.top-text {
  margin-bottom: 40px;
}

.vertical-text.bottom-text {
  margin-top: 40px;
}

/* Logo styling with glow effect */
.logo {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-svg {
  transform: scale(1.1);
  transition: transform 0.3s ease, filter 0.3s ease;
  /* Add more intense glow effect */
  filter: drop-shadow(0 0 5px var(--accent-glow)) drop-shadow(0 0 10px var(--accent-glow));
  animation: pulse-logo 4s infinite alternate;
}

.logo svg path {
  fill: #6c5ce7;
}

/* Hover effects */
.sidebar:hover .logo-svg {
  transform: scale(1.2);
}

.sidebar:hover .vertical-text {
  opacity: 1;
}

.vertical-text .letter:hover {
  color: #fff;
  text-shadow: 0 0 15px var(--accent-color), 0 0 30px var(--accent-color);
  transform: scale(1.2) rotate(5deg);
  transition: all 0.3s ease;
  animation: none;
}

/* Enhanced Animation keyframes */
@keyframes pulse-text {
  0% {
    color: var(--accent-color);
    text-shadow: 0 0 5px var(--accent-glow);
    transform: scale(1);
  }
  50% {
    color: var(--accent-hover);
    text-shadow: 0 0 12px var(--accent-glow), 0 0 20px var(--accent-glow);
    transform: scale(1.05);
  }
  100% {
    color: var(--accent-hover);
    text-shadow: 0 0 8px var(--accent-glow), 0 0 15px var(--accent-glow);
    transform: scale(1);
  }
}

@keyframes pulse-logo {
  0% {
    filter: drop-shadow(0 0 5px var(--accent-glow)) drop-shadow(0 0 8px var(--accent-glow));
  }
  50% {
    filter: drop-shadow(0 0 8px var(--accent-glow)) drop-shadow(0 0 15px var(--accent-glow));
  }
  100% {
    filter: drop-shadow(0 0 12px var(--accent-glow)) drop-shadow(0 0 20px var(--accent-glow));
  }
}

/* New enhanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes pulse-status {
  0% {
    transform: scale(1);
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 15px currentColor;
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 5px currentColor;
  }
}

@keyframes countUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 5px var(--accent-glow);
  }
  50% {
    box-shadow: 0 0 20px var(--accent-glow), 0 0 30px var(--accent-glow);
  }
  100% {
    box-shadow: 0 0 5px var(--accent-glow);
  }
}

@keyframes loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.main-content {
  flex: 1;
  padding: 25px;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  transform: scale(var(--ui-scale));
  transform-origin: top left;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.main-content::-webkit-scrollbar {
  display: none;
}

/* Enhanced Page styles with smooth transitions */
.page {
  display: none;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page.active {
  display: block;
  opacity: 1;
  transform: translateX(0);
  animation: fadeInUp 0.5s ease-out;
}

.page.slide-out {
  opacity: 0;
  transform: translateX(-20px);
}

/* Settings page styles */
.settings-title {
  color: var(--accent-color);
  font-size: 18px;
  margin-bottom: 20px;
  text-shadow: 0 0 5px var(--accent-glow);
}

.settings-select {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 5px;
  width: 100%;
  outline: none;
  transition: border-color 0.2s ease;
}

.settings-select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 5px var(--accent-glow);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

@media (max-width: 1300px) {
  .settings-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 900px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
}

.settings-section {
  background-color: var(--bg-secondary);
  border-radius: 10px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  animation: fadeInLeft 0.6s ease-out;
  min-width: 280px;
}

.settings-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--accent-color);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.setting-item:nth-child(1) { animation-delay: 0.1s; }
.setting-item:nth-child(2) { animation-delay: 0.2s; }
.setting-item:nth-child(3) { animation-delay: 0.3s; }
.setting-item:nth-child(4) { animation-delay: 0.4s; }
.setting-item:nth-child(5) { animation-delay: 0.5s; }

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 8px;
  margin: -8px;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: var(--text-secondary);
}

.star-icon {
  color: var(--accent-color);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.star-icon:hover {
  opacity: 1;
}

.toggle-switch {
  width: 46px;
  height: 22px;
  background-color: var(--bg-tertiary);
  border-radius: 11px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.toggle-switch:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px var(--accent-glow);
}

.toggle-switch.enabled {
  background-color: var(--accent-color);
  box-shadow: 0 0 15px var(--accent-glow);
  animation: glow-pulse 2s infinite;
}

.toggle-switch.loading {
  background-color: var(--warning-color);
  animation: loading-spin 1s linear infinite;
}

.toggle-slider {
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 3px;
  left: 3px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.toggle-switch:hover .toggle-slider {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.toggle-switch.enabled .toggle-slider {
  transform: translateX(24px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Ripple effect for toggle switches */
.toggle-switch::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.toggle-switch.ripple::before {
  width: 60px;
  height: 60px;
  animation: ripple 0.6s ease-out;
}

.slider-container {
  width: 100%;
  padding: 5px 0;
}

.slider {
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
}

.slider-fill {
  height: 100%;
  background-color: var(--accent-color);
  border-radius: 2px;
  position: relative;
}

.slider-fill::after {
  content: '';
  position: absolute;
  right: -6px;
  top: -4px;
  width: 12px;
  height: 12px;
  background-color: var(--accent-color);
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--accent-glow);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

/* Styles for active dragging state */
.slider.dragging .slider-fill::after {
  transform: scale(1.2);
  box-shadow: 0 0 0 3px var(--accent-glow);
}

/* Improve slider hover effect */
.slider:hover .slider-fill::after {
  transform: scale(1.1);
  box-shadow: 0 0 0 3px var(--accent-glow);
}

.setting-value {
  color: var(--accent-color);
  font-weight: bold;
  text-align: right;
  font-size: 14px;
}

/* Enhanced button styles with loading states */
.reset-button {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.reset-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.reset-button:hover::before {
  left: 100%;
}

.reset-button:hover {
  background-color: var(--accent-color);
  box-shadow: 0 4px 15px var(--accent-glow);
  transform: translateY(-2px);
}

.reset-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px var(--accent-glow);
}

.reset-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

.reset-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

/* Keybind button styles */
.keybind-button {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  letter-spacing: 0.5px;
  min-width: 60px;
  text-align: center;
  position: relative;
}

.keybind-button:hover {
  border-color: var(--accent-color);
  box-shadow: 0 0 5px var(--accent-glow);
}

.keybind-button.listening {
  background-color: var(--accent-color);
  color: white;
  animation: pulse-button 1.5s infinite;
}

@keyframes pulse-button {
  0% {
    box-shadow: 0 0 0 0 var(--accent-glow);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(0, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.status-indicator:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--danger-color);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 8px currentColor;
  position: relative;
}

.status-dot::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background-color: currentColor;
  opacity: 0.3;
  animation: pulse-status 2s infinite;
}

.status-dot.active {
  background-color: var(--success-color);
  color: var(--success-color);
  animation: pulse-status 1.5s infinite;
}

.status-dot.inactive {
  background-color: var(--danger-color);
  color: var(--danger-color);
}

.status-dot.warning {
  background-color: var(--warning-color);
  color: var(--warning-color);
  animation: pulse-status 1s infinite;
}

.status-text {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.status-indicator:hover .status-text {
  color: var(--text-primary);
}

/* Enhanced click counter with animation */
.setting-value {
  color: var(--accent-color);
  font-weight: bold;
  text-align: right;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
}

.setting-value.updated {
  animation: countUp 0.5s ease-out;
  color: var(--success-color);
  text-shadow: 0 0 10px currentColor;
}

.setting-value.updated::after {
  content: '+1';
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 12px;
  color: var(--success-color);
  animation: fadeInUp 0.8s ease-out;
  pointer-events: none;
}

/* Counter display with click rate */
.counter-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.click-rate {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 400;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.click-rate.active {
  color: var(--accent-color);
  opacity: 1;
  text-shadow: 0 0 5px var(--accent-glow);
  animation: pulse-text 1s ease-in-out;
}

.key-bind {
  background-color: #2a2a36;
  padding: 8px 12px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  color: #6c5ce7;
  font-size: 13px;
  letter-spacing: 0.5px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.bind-button {
  background-color: #2a2a36;
  color: #b8b8c0;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  letter-spacing: 0.5px;
}

.bind-button:hover {
  background-color: var(--accent-color);
  color: var(--text-secondary);
}

.info-text {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-button {
  flex: 1;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.3px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: 0;
}

.control-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.control-button:hover::before {
  left: 100%;
}

.control-button:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  box-shadow: 0 4px 15px var(--accent-glow);
  transform: translateY(-2px);
}

.control-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px var(--accent-glow);
}

.control-button.disabled,
.control-button:disabled {
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.5;
  box-shadow: none;
  transform: none;
}

.control-button.disabled:hover,
.control-button:disabled:hover {
  background-color: var(--bg-primary);
  box-shadow: none;
  transform: none;
}

.control-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

.control-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
}

/* Default select styling */
select {
  background-color: #2a2a36;
  color: #e6e6e6;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  appearance: none;
  position: relative;
  padding-right: 30px;
  transition: all 0.2s ease;
}

select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.3);
}

select option {
  background-color: #16161e;
  color: #e6e6e6;
}

/* Custom dropdown arrow */
.select-wrapper {
  position: relative;
  display: inline-block;
}

.select-wrapper::after {
  content: '▼';
  font-size: 10px;
  color: #6c5ce7;
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.bind-button:hover {
  background-color: #6c5ce7;
}

.button-group {
  display: flex;
  gap: 10px;
}

.control-button {
  flex: 1;
  background-color: #333355;
  color: #e6e6e6;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.control-button:hover {
  background-color: #6c5ce7;
}

#start-button {
  background-color: #2a623d;
}

#start-button:hover {
  background-color: #3a8a55;
}

#stop-button {
  background-color: #622a2a;
}

#stop-button:hover {
  background-color: #8a3a3a;
}

.info-text {
  font-size: 12px;
  line-height: 1.5;
  color: #aaaaaa;
}

/* Bloxstrap Integration Styles */
.fflag-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid var(--border-color);
}

.fflag-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  gap: 8px;
  min-height: 32px;
}

.fflag-item:last-child {
  border-bottom: none;
}

.fflag-name {
  font-size: 11px;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  flex: 1;
  word-break: break-all;
  line-height: 1.3;
  max-width: 180px;
}

.fflag-value {
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  min-width: 60px;
  max-width: 80px;
  text-align: center;
  flex-shrink: 0;
  white-space: nowrap;
}

.fflag-value.not-set {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.fflag-value.enabled {
  background-color: var(--success-color);
  color: white;
}

.fflag-value.disabled {
  background-color: var(--danger-color);
  color: white;
}

.info-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: help;
  margin-left: 8px;
}

.info-icon:hover {
  background-color: var(--accent-hover);
  transform: scale(1.1);
}

/* Bloxstrap status indicator */
#bloxstrap-status .status-dot.detected {
  background-color: var(--success-color);
  color: var(--success-color);
  animation: pulse-status 1.5s infinite;
}

#bloxstrap-status .status-dot.not-detected {
  background-color: var(--warning-color);
  color: var(--warning-color);
}

/* FFlag button states */
.control-button.fflag-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.control-button.fflag-success:hover {
  background-color: #059669;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
}

.control-button.fflag-error {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.control-button.fflag-error:hover {
  background-color: #dc2626;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

/* Game Launcher Styles */
.input-group {
  display: flex;
  gap: 8px;
  width: 100%;
}

.game-id-input {
  flex: 1;
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 13px;
  outline: none;
  transition: all 0.2s ease;
}

.game-id-input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 8px var(--accent-glow);
}

.game-id-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

.recent-games-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px;
  overflow-y: auto;
  padding-right: 5px;
}

.recent-game-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bg-tertiary);
  border-radius: 5px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.recent-game-item:hover {
  background-color: rgba(108, 92, 231, 0.2);
  transform: translateX(3px);
}

.game-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.game-name {
  font-size: 13px;
  color: var(--text-primary);
}

.game-id {
  font-size: 11px;
  color: var(--text-secondary);
}

.launch-icon {
  color: var(--accent-color);
  font-size: 16px;
}

.empty-list-message {
  color: var(--text-secondary);
  font-size: 13px;
  text-align: center;
  padding: 10px;
  font-style: italic;
  opacity: 0.7;
}

/* Toast Notifications */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  z-index: 9999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast-notification.show {
  transform: translateY(0);
  opacity: 1;
}

.toast-success {
  background-color: var(--success-color);
}

.toast-error {
  background-color: var(--danger-color);
}

.toast-warning {
  background-color: var(--warning-color);
  color: #333;
}

.toast-info {
  background-color: var(--accent-color);
}
